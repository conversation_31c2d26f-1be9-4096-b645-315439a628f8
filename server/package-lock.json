{"name": "distribution-operation-web", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "distribution-operation-web", "version": "1.0.0", "license": "LGPL", "dependencies": {"@eagle/umc-selector-node": "^5.0.0", "@eagle/workflow-node": "^4.0.0", "@eagler/authorizion-node": "^1.0.0", "@eagler/bussiness-components-node": "^0.0.45", "@tiger/apolloy": "^4.0.0", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.0.0", "@tiger/ejs": "^4.0.0", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.0", "@tiger/info": "^4.0.0", "@tiger/logger": "^4.0.0", "@tiger/microconfig": "0.0.3", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/security": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/tiger-extract-header": "0.0.4", "@tiger/validator": "^4.0.0", "boom": "^7.1.1", "koa": "^2.5.3", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "path-to-regexp": "^1.1.1"}, "devDependencies": {"@types/boom": "^7.2.0", "@types/chai": "^4.1.7", "@types/ejs": "^2.6.3", "@types/joi": "^14.3.2", "@types/koa": "^2.0.43", "@types/koa-etag": "^3.0.0", "@types/koa-router": "7.0.35", "@types/koa-send": "^4.1.1", "@types/node": "^8.10.29", "@types/supertest": "^2.0.6", "@vscode-snippets/tiger": "^1.0.0", "chai": "^4.2.0", "fs-extra": "^7.0.0", "get-port": "^3.2.0", "jasmine": "^3.3.1", "jasmine-ts": "^0.3.0", "nodemon": "^2.0.4", "supertest": "^3.3.0", "ts-node": "^6.1.1", "tslint": "^5.12.1", "typescript": "^3.0.0", "yargs": "^7.0.2"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@babel/code-frame": {"version": "7.15.8", "resolved": "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "dependencies": {"@babel/highlight": "^7.14.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k=", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.14.5", "resolved": "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "dependencies": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "node_modules/@eagle/common-service-node": {"version": "3.0.3", "resolved": "http://npm.mail.netease.com/registry/@eagle/common-service-node/-/@eagle/common-service-node-3.0.3.tgz", "integrity": "sha1-6+MeQe29jx2AqgVHhmUWhIxuBjg=", "dependencies": {"@tiger/boot": "^3.0.0", "@tiger/core": "^3.0.0", "@tiger/openid": "^3.0.0", "@tiger/proxy": "^3.0.0", "@tiger/request": "^1.0.5", "@tiger/swagger": "3.0.6", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "debug": "^4.1.0", "request": "^2.88.0", "request-promise": "^4.2.2"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/boot": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/-/@tiger/boot-3.0.1.tgz", "integrity": "sha1-dmrW5RGzc/Zr9QKif560QPkjEXo=", "dependencies": {"@tiger/error": "^3.0.0", "cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/core": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/-/@tiger/core-3.0.2.tgz", "integrity": "sha1-ixx53GPOYhWuKgkRLfBNrafPRTE=", "dependencies": {"@tiger/tslint": "^3.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/error": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/-/@tiger/error-3.0.0.tgz", "integrity": "sha1-mYYuvQse9eptp+ouGp1B35krYZA=", "dependencies": {"@tiger/logger": "^3.0.0", "@types/boom": "^7.2.1", "@types/koa": "^2.0.46", "boom": "^7.2.2", "koa": "^2.6.1"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/filter": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/-/@tiger/filter-3.0.0.tgz", "integrity": "sha1-dk5fVgiezmftgBguLdOi7of3g04=", "dependencies": {"@tiger/core": "^3.0.0", "path-to-regexp": "^1.7.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/logger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/-/@tiger/logger-3.0.6.tgz", "integrity": "sha1-WLV7FfCWQ20ZYUlmg/ajHn7sQ2g=", "dependencies": {"@tiger/filter": "^3.0.0", "log4js": "^4.0.2"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/openid": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/-/@tiger/openid-3.0.1.tgz", "integrity": "sha1-RG/whZYW9mrv44B3uWj6GSjY5JM=", "dependencies": {"@tiger/core": "^3.0.0", "@tiger/filter": "^3.0.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/proxy": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/-/@tiger/proxy-3.0.0.tgz", "integrity": "sha1-o6AC83NebS2YD+NyNgwdouXQCXE=", "dependencies": {"@tiger/core": "^3.0.0", "@tiger/logger": "^3.0.0", "@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0", "path-to-regexp": "^1.7.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request": {"version": "1.0.5", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/-/@tiger/request-1.0.5.tgz", "integrity": "sha1-SZLfxqk0qE5tOddJO6xCgEpb5K4=", "dependencies": {"@tiger/core": "^1.0.1", "@tiger/logger": "^1.0.0", "axios": "^0.18.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/@tiger/core": {"version": "1.0.25", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/-/@tiger/core-1.0.25.tgz", "integrity": "sha1-0/tkAxu8JCbidJPOt7i5kf3jLnM=", "dependencies": {"@tiger/tslint": "^1.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/@tiger/filter": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/-/@tiger/filter-1.0.2.tgz", "integrity": "sha1-N215eL2ACEtIJwpV2oZCl6SsxhU=", "dependencies": {"@tiger/core": "^1.0.1", "path-to-regexp": "^1.7.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/@tiger/logger": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/-/@tiger/logger-1.0.3.tgz", "integrity": "sha1-mUoM6udRh4LdI8iRbmkEwP0GUMY=", "dependencies": {"@tiger/core": "^1.0.1", "@tiger/filter": "^1.0.0", "log4js": "^3.0.6"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/@tiger/tslint": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/-/@tiger/tslint-1.0.2.tgz", "integrity": "sha1-oXELDrApdS0mUS3g0NsG/22cC4c=", "dependencies": {"tslint": "^5.12.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/request/node_modules/log4js": {"version": "3.0.6", "resolved": "https://registry.npmmirror.com/log4js/-/log4js-3.0.6.tgz", "integrity": "sha512-ezXZk6oPJCWL483zj64pNkMuY/NcRX5MPiB0zE6tjZM137aeusrOnW1ecxgF9cmwMWkBMhjteQxBPoZBh9FDxQ==", "deprecated": "3.x is no longer supported. Please upgrade to 6.x or higher.", "dependencies": {"circular-json": "^0.5.5", "date-format": "^1.2.0", "debug": "^3.1.0", "rfdc": "^1.1.2", "streamroller": "0.7.0"}, "engines": {"node": ">=6.0"}}, "node_modules/@eagle/common-service-node/node_modules/@tiger/swagger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/-/@tiger/swagger-3.0.6.tgz", "integrity": "sha1-lGoReNAUGtZLPt0VtmG/c4amyMg=", "dependencies": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}}, "node_modules/@eagle/common-service-node/node_modules/date-format": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/date-format/-/date-format-1.2.0.tgz", "integrity": "sha512-lAJqBmFzCLcDJdI9cEnJ7loSkLTh1PbIgZUndlzvYbf6NyFEr5n9rQhOwr6CIGwZqyQ3sYeQQiP9NOVQmgmRMA==", "deprecated": "1.x is no longer supported. Please upgrade to 4.x or higher.", "engines": {"node": ">=4.0"}}, "node_modules/@eagle/common-service-node/node_modules/koa-send": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/koa-send/-/koa-send-5.0.1.tgz", "integrity": "sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==", "dependencies": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}, "engines": {"node": ">= 8"}}, "node_modules/@eagle/common-service-node/node_modules/streamroller": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/streamroller/-/streamroller-0.7.0.tgz", "integrity": "sha512-WREzfy0r0zUqp3lGO096wRuUp7ho1X6uo/7DJfTlEi0Iv/4gT7YHqXDjKC2ioVGBZtE8QzsQD9nx1nIuoZ57jQ==", "deprecated": "0.x is no longer supported. Please upgrade to 3.x or higher.", "dependencies": {"date-format": "^1.2.0", "debug": "^3.1.0", "mkdirp": "^0.5.1", "readable-stream": "^2.3.0"}, "engines": {"node": ">=0.12.0"}}, "node_modules/@eagle/common-service-node/node_modules/streamroller/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dependencies": {"ms": "^2.1.1"}}, "node_modules/@eagle/umc-selector-node": {"version": "5.0.0", "resolved": "http://npm.mail.netease.com/registry/@eagle/umc-selector-node/-/@eagle/umc-selector-node-5.0.0.tgz", "integrity": "sha1-+nN3Jd/o+AdcyigcPMdtJsaf6JI=", "dependencies": {"@tiger/filter": "^4.0.65", "@tiger/logger": "^4.0.65", "@tiger/tiger-extract-header": "^0.0.2"}}, "node_modules/@eagle/umc-selector-node/node_modules/@tiger/tiger-extract-header": {"version": "0.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/-/@tiger/tiger-extract-header-0.0.2.tgz", "integrity": "sha1-W0ENAngdXZNAQqqP5d2q3ob9ft0=", "dependencies": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}, "node_modules/@eagle/workflow-node": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@eagle/workflow-node/download/@eagle/workflow-node-4.0.2.tgz", "integrity": "sha1-tYsggiTh5LCEHW3CoqqzMTXISlg="}, "node_modules/@eagler/authorizion-node": {"version": "1.3.4", "resolved": "http://npm.mail.netease.com/registry/@eagler/authorizion-node/download/@eagler/authorizion-node-1.3.4.tgz", "integrity": "sha1-tLo5l6bR+rLygGB9PHAB35fhY4I=", "dependencies": {"@tiger/tiger-extract-header": "^0.0.3"}}, "node_modules/@eagler/authorizion-node/node_modules/@tiger/tiger-extract-header": {"version": "0.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/download/@tiger/tiger-extract-header-0.0.3.tgz", "integrity": "sha1-LRYiryEUQDVAoCCLCpbmsG0YDrs=", "dependencies": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}, "node_modules/@eagler/bussiness-components-node": {"version": "0.0.45", "resolved": "http://npm.mail.netease.com/registry/@eagler/bussiness-components-node/-/@eagler/bussiness-components-node-0.0.45.tgz", "integrity": "sha1-U+zukOtteygFnMRNGxClcKX+mig=", "dependencies": {"@eagle/common-service-node": "^3.0.3", "@eagle/workflow-node": "^4.0.0", "@tiger/apolloy": "^4.0.0", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.0.0", "@tiger/ejs": "^4.0.0", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.0", "@tiger/info": "^4.0.0", "@tiger/logger": "^4.0.0", "@tiger/microconfig": "0.0.3", "@tiger/monitor": "^4.0.42", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/security": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/validator": "^4.0.0", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "boom": "^7.1.1", "debug": "^4.1.0", "koa": "^2.5.3", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-multer": "^1.0.2", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "nos-node-sdk": "0.0.4", "path-to-regexp": "^1.1.1", "request": "^2.88.0", "request-promise": "^4.2.2"}}, "node_modules/@node-common/collector": {"version": "0.0.12", "resolved": "http://npm.mail.netease.com/registry/@node-common/collector/download/@node-common/collector-0.0.12.tgz", "integrity": "sha1-6lbQjK21K5/5rCkK7eA0mTBEMMc=", "dependencies": {"debug": "^4.1.1", "kafka-node": "^5.0.0"}}, "node_modules/@node-common/collector/node_modules/kafka-node": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/kafka-node/download/kafka-node-5.0.0.tgz", "integrity": "sha1-S29lzB136+VlhZ37j5V17RXVQ8A=", "dependencies": {"async": "^2.6.2", "binary": "~0.3.0", "bl": "^2.2.0", "buffer-crc32": "~0.2.5", "buffermaker": "~1.2.0", "debug": "^2.1.3", "denque": "^1.3.0", "lodash": "^4.17.4", "minimatch": "^3.0.2", "nested-error-stacks": "^2.0.0", "optional": "^0.1.3", "retry": "^0.10.1", "uuid": "^3.0.0"}, "engines": {"node": ">=8.5.1"}, "optionalDependencies": {"snappy": "^6.0.1"}}, "node_modules/@node-common/collector/node_modules/kafka-node/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dependencies": {"ms": "2.0.0"}}, "node_modules/@node-common/collector/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/@sindresorhus/is": {"version": "0.14.0", "resolved": "https://registry.npmmirror.com/@sindresorhus/is/download/@sindresorhus/is-0.14.0.tgz", "integrity": "sha1-n7OjzzEyMoFR81PeRjLgHlIQK+o=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/@szmarczak/http-timer": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@szmarczak/http-timer/download/@szmarczak/http-timer-1.1.2.tgz", "integrity": "sha1-sWZeLEYaLNkvTBu/UNVFTeDUtCE=", "dev": true, "dependencies": {"defer-to-connect": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/@tiger/apolloy": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/apolloy/download/@tiger/apolloy-4.4.1.tgz", "integrity": "sha1-Z9ZAF6KEHDahaTtB+GnLg6QMBoM=", "dependencies": {"@tiger/logger": "^4.4.1", "fs-extra": "^7.0.0", "reflect-metadata": "^0.1.12"}}, "node_modules/@tiger/boot": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/download/@tiger/boot-4.4.1.tgz", "integrity": "sha1-ZGxceTfc1DF1BmTcH1ZBUO96fog=", "dependencies": {"@node-common/collector": "^0.0.12", "cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "node_modules/@tiger/cache": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/cache/download/@tiger/cache-4.4.1.tgz", "integrity": "sha1-T4DgviEO6f5iGLE55c37H1yneNI=", "dependencies": {"@types/debug": "^4.1.4", "@types/lru-cache": "^4.0.0", "debug": "^4.1.1", "lru-cache": "^4.0.0", "node-object-hash": "^1.4.2", "redis": "^2.8.0"}}, "node_modules/@tiger/cls-hooked": {"version": "4.3.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/cls-hooked/download/@tiger/cls-hooked-4.3.0.tgz", "integrity": "sha1-bh4kavw2U4vybVps/vuHYWPouog=", "dependencies": {"async-hook-jl": "^1.7.6", "emitter-listener": "^1.0.1"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3 || >=8.2.1"}}, "node_modules/@tiger/core": {"version": "4.4.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/download/@tiger/core-4.4.0.tgz", "integrity": "sha1-23+GE+n/NQIeYx6KzmF5G8rZ2Jo=", "dependencies": {"@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "2.11.3", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "node_modules/@tiger/ejs": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/ejs/download/@tiger/ejs-4.4.1.tgz", "integrity": "sha1-jkSEpKkVuoEr2Hf4B/iMHmmc6VE=", "dependencies": {"koa-ejs": "^4.1.2", "shimmer": "^1.2.0"}}, "node_modules/@tiger/error": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/download/@tiger/error-4.4.1.tgz", "integrity": "sha1-fkp4Sj98PnLXJ8d+5AfBp1jhX+8=", "dependencies": {"@types/boom": "^7.2.1", "@types/koa": "2.11.3", "boom": "^7.2.2", "koa": "^2.6.1"}}, "node_modules/@tiger/filter": {"version": "4.4.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/download/@tiger/filter-4.4.0.tgz", "integrity": "sha1-iKW0k03mZwng9+hC8FUnG6lSroE=", "dependencies": {"koa": "^2.6.1", "path-to-regexp": "^1.7.0"}}, "node_modules/@tiger/health": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/health/download/@tiger/health-4.4.1.tgz", "integrity": "sha1-+F7WI1odPPV7xg+9dzJ0VwFG1n8="}, "node_modules/@tiger/info": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/info/download/@tiger/info-4.4.1.tgz", "integrity": "sha1-BqRsJq0ZubGDmN31Buzs4lvpPhQ="}, "node_modules/@tiger/logger": {"version": "4.4.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/download/@tiger/logger-4.4.2.tgz", "integrity": "sha1-bHJU8AOvh0046kaPVFxbwhYA5no=", "dependencies": {"@tiger/trace": "^4.4.1", "log4js": "^4.0.2"}}, "node_modules/@tiger/microconfig": {"version": "0.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/microconfig/download/@tiger/microconfig-0.0.3.tgz", "integrity": "sha1-hVnN5J3QGO1QTCNjEPsUNhfSvXs=", "dependencies": {"koa": "^2.7.0"}}, "node_modules/@tiger/monitor": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/monitor/-/@tiger/monitor-4.4.1.tgz", "integrity": "sha1-9z8DjwQv0XUSlW+EoAYWQlurnHw=", "dependencies": {"axios": "^0.19.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13", "shimmer": "^1.2.1"}}, "node_modules/@tiger/monitor/node_modules/axios": {"version": "0.19.2", "resolved": "https://registry.npmmirror.com/axios/-/axios-0.19.2.tgz", "integrity": "sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dependencies": {"follow-redirects": "1.5.10"}}, "node_modules/@tiger/monitor/node_modules/debug": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "dependencies": {"ms": "2.0.0"}}, "node_modules/@tiger/monitor/node_modules/follow-redirects": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.5.10.tgz", "integrity": "sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==", "dependencies": {"debug": "=3.1.0"}, "engines": {"node": ">=4.0"}}, "node_modules/@tiger/monitor/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/@tiger/openid": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/download/@tiger/openid-4.0.2.tgz", "integrity": "sha1-cyKjzLuOG72Ptpe8zgRoBGshvHM="}, "node_modules/@tiger/permission": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/permission/download/@tiger/permission-4.1.0.tgz", "integrity": "sha1-ZntWya+cQFhhGTLK0IWNL4NHpAs=", "dependencies": {"@types/koa": "^2.0.46", "koa": "^2.5.1"}}, "node_modules/@tiger/proxy": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/download/@tiger/proxy-4.4.1.tgz", "integrity": "sha1-AZ19YbP+jpl7S1geDVdCtYn9C2o=", "dependencies": {"@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0"}}, "node_modules/@tiger/request": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/download/@tiger/request-4.4.1.tgz", "integrity": "sha1-RM+O3Z1ZFj8kMESqXRZUqego3EY=", "dependencies": {"axios": "^0.18.0"}}, "node_modules/@tiger/security": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/security/download/@tiger/security-4.4.1.tgz", "integrity": "sha1-ZlsoMxXyvKFUDipPi8SV2kncvtA="}, "node_modules/@tiger/session": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/session/download/@tiger/session-4.4.1.tgz", "integrity": "sha1-DpluxJeFb4HSTDF5jyoy1UUSvSc=", "dependencies": {"@types/koa": "2.11.3", "@types/redis": "^2.8.6", "koa": "^2.5.1", "lru-cache": "^4.1.1", "redis": "^2.8.0"}}, "node_modules/@tiger/swagger": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/download/@tiger/swagger-4.4.1.tgz", "integrity": "sha1-eFYO6I+OwExvXDaE+/VcW9cSAho=", "dependencies": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}}, "node_modules/@tiger/swagger/node_modules/koa-send": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/koa-send/download/koa-send-5.0.1.tgz", "integrity": "sha1-Odzuv6+zldDWC+r/ujpwtPVD/nk=", "dependencies": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}, "engines": {"node": ">= 8"}}, "node_modules/@tiger/tiger-extract-header": {"version": "0.0.4", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/download/@tiger/tiger-extract-header-0.0.4.tgz", "integrity": "sha1-7xYL9voxcz6FnWUAcF+BbrNI3Gk=", "dependencies": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}, "node_modules/@tiger/trace": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/trace/download/@tiger/trace-4.4.1.tgz", "integrity": "sha1-psHh26UP2N8l+SoTpWx951rXrAg=", "dependencies": {"@tiger/cls-hooked": "^4.3.0", "kafka-node": "^4.1.3", "semver": "^6.1.2", "shimmer": "^1.2.1"}}, "node_modules/@tiger/tslint": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/-/@tiger/tslint-3.0.0.tgz", "integrity": "sha1-kP/yftI8eupyO4LbelJYwq2Bh94="}, "node_modules/@tiger/validator": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/validator/download/@tiger/validator-4.4.1.tgz", "integrity": "sha1-3w7sN2hY4EiHFYQZu9YhcrEoYRE=", "dependencies": {"class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13"}}, "node_modules/@types/accepts": {"version": "1.3.5", "resolved": "https://registry.npmmirror.com/@types/accepts/download/@types/accepts-1.3.5.tgz", "integrity": "sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=", "dependencies": {"@types/node": "*"}}, "node_modules/@types/accepts/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/bluebird": {"version": "3.5.36", "resolved": "https://registry.npmmirror.com/@types/bluebird/-/bluebird-3.5.36.tgz", "integrity": "sha512-HBNx4lhkxN7bx6P0++W8E289foSu8kO8GCk2unhuVggO+cE7rh9DhZUyPhUxNRG9m+5B5BTKxZQ5ZP92x/mx9Q=="}, "node_modules/@types/body-parser": {"version": "1.19.1", "resolved": "https://registry.npmmirror.com/@types/body-parser/download/@types/body-parser-1.19.1.tgz", "integrity": "sha1-DAF0xCp9AXuBgwPUtdlpywt1kpw=", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/body-parser/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/boom": {"version": "7.3.1", "resolved": "https://registry.npmmirror.com/@types/boom/download/@types/boom-7.3.1.tgz", "integrity": "sha1-r+w+6e1WrCL13/xFrjOpP8D5pIE="}, "node_modules/@types/caseless": {"version": "0.12.2", "resolved": "https://registry.npmmirror.com/@types/caseless/-/caseless-0.12.2.tgz", "integrity": "sha512-6ckxMjBBD8URvjB6J3NcnuAn5Pkl7t3TizAg+xdlzzQGSPSmBcXf8KoIH0ua/i+tio+ZRUHEXp0HEmvaR4kt0w=="}, "node_modules/@types/chai": {"version": "4.2.22", "resolved": "https://registry.npmmirror.com/@types/chai/download/@types/chai-4.2.22.tgz", "integrity": "sha1-RwINfkzxkZTUO1IC8191vSrTXOc=", "dev": true}, "node_modules/@types/connect": {"version": "3.4.35", "resolved": "https://registry.npmmirror.com/@types/connect/download/@types/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmmirror.com/@types/content-disposition/download/@types/content-disposition-0.5.4.tgz", "integrity": "sha1-3kjPAcecnxVgvP2K5DIXqwKGV/g="}, "node_modules/@types/cookiejar": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/@types/cookiejar/download/@types/cookiejar-2.1.2.tgz", "integrity": "sha1-Zq2TMfY/6KPT2djG45Bt0Q9kRug=", "dev": true}, "node_modules/@types/cookies": {"version": "0.7.7", "resolved": "https://registry.npmmirror.com/@types/cookies/download/@types/cookies-0.7.7.tgz", "integrity": "sha1-epJFPR0WOJwFpTAe71ZvNJRs/YE=", "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}}, "node_modules/@types/cookies/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/debug": {"version": "4.1.7", "resolved": "https://registry.npmmirror.com/@types/debug/download/@types/debug-4.1.7.tgz", "integrity": "sha1-fMDqdhUJEkcJuLLRCQ2PbBeq24I=", "dependencies": {"@types/ms": "*"}}, "node_modules/@types/ejs": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/@types/ejs/download/@types/ejs-2.7.0.tgz", "integrity": "sha1-vITgg+rjj2Sih6bauQErvh2W4pU=", "dev": true}, "node_modules/@types/etag": {"version": "1.8.1", "resolved": "https://registry.npmmirror.com/@types/etag/download/@types/etag-1.8.1.tgz", "integrity": "sha1-WTyo3bQ6yz2wSb0JVf1k0oGrWLk=", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/express": {"version": "4.17.13", "resolved": "https://registry.npmmirror.com/@types/express/download/@types/express-4.17.13.tgz", "integrity": "sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.17.24", "resolved": "https://registry.npmmirror.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.24.tgz", "integrity": "sha1-6kH5O/fg1ZzVp2ZlBo7WqraBXAc=", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/@types/express-serve-static-core/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/formidable": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/@types/formidable/download/@types/formidable-1.2.3.tgz", "integrity": "sha1-me5f1Or7qpVEjItnDl6OOJ8q84A=", "dependencies": {"@types/node": "*"}}, "node_modules/@types/formidable/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/http-assert": {"version": "1.5.3", "resolved": "https://registry.npmmirror.com/@types/http-assert/download/@types/http-assert-1.5.3.tgz", "integrity": "sha1-7449Go1Gw4fwSrDy6KuMsMUHhmE="}, "node_modules/@types/http-proxy": {"version": "1.17.7", "resolved": "https://registry.npmmirror.com/@types/http-proxy/download/@types/http-proxy-1.17.7.tgz", "integrity": "sha1-MOqFzCyGg2g1Kjfw0NNYHiSDTG8=", "dependencies": {"@types/node": "*"}}, "node_modules/@types/http-proxy/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/joi": {"version": "14.3.4", "resolved": "https://registry.npmmirror.com/@types/joi/download/@types/joi-14.3.4.tgz", "integrity": "sha1-7tHhTLsHcWB5yBQTiDGlIKcloeA=", "dev": true}, "node_modules/@types/keygrip": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/@types/keygrip/download/@types/keygrip-1.0.2.tgz", "integrity": "sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI="}, "node_modules/@types/koa": {"version": "2.11.3", "resolved": "https://registry.npmmirror.com/@types/koa/download/@types/koa-2.11.3.tgz", "integrity": "sha1-VA7ON2WBsSvq35pBfdFzG8McFs4=", "dependencies": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}}, "node_modules/@types/koa-compose": {"version": "3.2.5", "resolved": "https://registry.npmmirror.com/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz", "integrity": "sha1-hesugKxQvpXzfM+MQHwJu+NGjp0=", "dependencies": {"@types/koa": "*"}}, "node_modules/@types/koa-etag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/@types/koa-etag/download/@types/koa-etag-3.0.0.tgz", "integrity": "sha1-0U09q0XVV3uUvHKWBjHelnUTQdM=", "dev": true, "dependencies": {"@types/etag": "*", "@types/koa": "*"}}, "node_modules/@types/koa-router": {"version": "7.0.35", "resolved": "https://registry.npmmirror.com/@types/koa-router/download/@types/koa-router-7.0.35.tgz", "integrity": "sha1-B0QqyV5l7BsEKvT3Kr6kTclrkJ8=", "dev": true, "dependencies": {"@types/koa": "*"}}, "node_modules/@types/koa-send": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/@types/koa-send/download/@types/koa-send-4.1.3.tgz", "integrity": "sha1-Fxk8ZHKunl0bma6AhpScxP1pF50=", "dev": true, "dependencies": {"@types/koa": "*"}}, "node_modules/@types/koa/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/lru-cache": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/@types/lru-cache/download/@types/lru-cache-4.1.3.tgz", "integrity": "sha1-7F623YGLegYzbPtzaHIxZLGV+Bg="}, "node_modules/@types/mime": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/@types/mime/download/@types/mime-1.3.2.tgz", "integrity": "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o="}, "node_modules/@types/ms": {"version": "0.7.31", "resolved": "https://registry.npmmirror.com/@types/ms/download/@types/ms-0.7.31.tgz", "integrity": "sha1-MbfKZAcSij0rvCf+LSGzRTl/YZc="}, "node_modules/@types/node": {"version": "8.10.66", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-8.10.66.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-8.10.66.tgz", "integrity": "sha1-3QNdQJ3zIqzIPf9ipgLxKleDu7M="}, "node_modules/@types/qs": {"version": "6.9.7", "resolved": "https://registry.npmmirror.com/@types/qs/download/@types/qs-6.9.7.tgz?cache=0&sync_timestamp=1629708791613&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss="}, "node_modules/@types/range-parser": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/@types/range-parser/download/@types/range-parser-1.2.4.tgz?cache=0&sync_timestamp=1629708789398&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw="}, "node_modules/@types/redis": {"version": "2.8.32", "resolved": "https://registry.npmmirror.com/@types/redis/download/@types/redis-2.8.32.tgz", "integrity": "sha1-HTQwIZr77hD4z6OJ2tJXGgXs+xE=", "dependencies": {"@types/node": "*"}}, "node_modules/@types/redis/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/request": {"version": "2.48.8", "resolved": "https://registry.npmmirror.com/@types/request/-/request-2.48.8.tgz", "integrity": "sha512-whjk1EDJPcAR2kYHRbFl/lKeeKYTi05A15K9bnLInCVroNDCtXce57xKdI0/rQaA3K+6q0eFyUBPmqfSndUZdQ==", "dependencies": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.0"}}, "node_modules/@types/request-promise": {"version": "4.1.48", "resolved": "https://registry.npmmirror.com/@types/request-promise/-/request-promise-4.1.48.tgz", "integrity": "sha512-sLsfxfwP5G3E3U64QXxKwA6ctsxZ7uKyl4I28pMj3JvV+ztWECRns73GL71KMOOJME5u1A5Vs5dkBqyiR1Zcnw==", "dependencies": {"@types/bluebird": "*", "@types/request": "*"}}, "node_modules/@types/serve-static": {"version": "1.13.10", "resolved": "https://registry.npmmirror.com/@types/serve-static/download/@types/serve-static-1.13.10.tgz", "integrity": "sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static/node_modules/@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}, "node_modules/@types/superagent": {"version": "4.1.13", "resolved": "https://registry.npmmirror.com/@types/superagent/download/@types/superagent-4.1.13.tgz", "integrity": "sha1-Cqo/T/lAS5STLR3N+389OdI5l6A=", "dev": true, "dependencies": {"@types/cookiejar": "*", "@types/node": "*"}}, "node_modules/@types/supertest": {"version": "2.0.11", "resolved": "https://registry.npmmirror.com/@types/supertest/download/@types/supertest-2.0.11.tgz", "integrity": "sha1-LnD2nyILx3tPZg1ywuGkIx9Ep30=", "dev": true, "dependencies": {"@types/superagent": "*"}}, "node_modules/@types/tough-cookie": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/@types/tough-cookie/-/tough-cookie-4.0.2.tgz", "integrity": "sha512-Q5vtl1W5ue16D+nIaW8JWebSSraJVlK+EthKn7e7UcD4KWsaSJ8BqGPXNaPghgtcn/fhvrN17Tv8ksUsQpiplw=="}, "node_modules/@vscode-snippets/tiger": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/@vscode-snippets/tiger/download/@vscode-snippets/tiger-1.0.0.tgz", "integrity": "sha1-5y/rBjmBDcPYMYuzQgqbRM77VjY=", "dev": true}, "node_modules/abbrev": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true}, "node_modules/accepts": {"version": "1.3.7", "resolved": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "dependencies": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-align": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/ansi-align/download/ansi-align-3.0.1.tgz?cache=0&sync_timestamp=1632743770230&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-align%2Fdownload%2Fansi-align-3.0.1.tgz", "integrity": "sha1-DN8S4RGs53OobpofrRIlxDyxmlk=", "dev": true, "dependencies": {"string-width": "^4.1.0"}}, "node_modules/ansi-align/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/ansi-align/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/any-promise": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8="}, "node_modules/anymatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.2.tgz", "integrity": "sha1-wFV8CWrzLxBhmPT04qODU343hxY=", "dev": true, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/append-field": {"version": "0.1.0", "resolved": "https://registry.npmmirror.com/append-field/-/append-field-0.1.0.tgz", "integrity": "sha512-8BgHoIwbQZaAQgDZLBu2vQoXHgUpSx4vQK1qv7e6R8YfbiSf4fCaBPJRtM1BaxVn1rIHc5ftv0cklsJ78BkouQ=="}, "node_modules/aproba": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/aproba/download/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "optional": true}, "node_modules/are-we-there-yet": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz", "integrity": "sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=", "deprecated": "This package is no longer supported.", "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/argparse": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arrify": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/arrify/download/arrify-1.0.1.tgz?cache=0&sync_timestamp=1619599497996&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Farrify%2Fdownload%2Farrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.2.6", "resolved": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw==", "engines": {"node": ">=0.8"}}, "node_modules/assertion-error": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/assertion-error/download/assertion-error-1.1.0.tgz?cache=0&sync_timestamp=1633447542062&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fassertion-error%2Fdownload%2Fassertion-error-1.1.0.tgz", "integrity": "sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs=", "dev": true, "engines": {"node": "*"}}, "node_modules/async": {"version": "2.6.3", "resolved": "https://registry.npmmirror.com/async/download/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/async-hook-jl": {"version": "1.7.6", "resolved": "https://registry.npmmirror.com/async-hook-jl/download/async-hook-jl-1.7.6.tgz", "integrity": "sha1-T9JcL4ZNuvJ5xhDXO/l7GyhZXmg=", "dependencies": {"stack-chain": "^1.3.7"}, "engines": {"node": "^4.7 || >=6.9 || >=7.3"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA==", "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.11.0", "resolved": "https://registry.npmmirror.com/aws4/-/aws4-1.11.0.tgz", "integrity": "sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA=="}, "node_modules/axios": {"version": "0.18.1", "resolved": "https://registry.npmmirror.com/axios/download/axios-0.18.1.tgz", "integrity": "sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dependencies": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}}, "node_modules/axios/node_modules/debug": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "dependencies": {"ms": "2.0.0"}}, "node_modules/axios/node_modules/follow-redirects": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.5.10.tgz?cache=0&sync_timestamp=1631622129411&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "dependencies": {"debug": "=3.1.0"}, "engines": {"node": ">=4.0"}}, "node_modules/axios/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&sync_timestamp=1617714233441&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/binary": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/binary/download/binary-0.3.0.tgz", "integrity": "sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=", "dependencies": {"buffers": "~0.1.1", "chainsaw": "~0.1.0"}, "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/bindings": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "optional": true, "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bl": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/bl/download/bl-2.2.1.tgz", "integrity": "sha1-jBGntzBlXF1WiYzchxIk9A/ZAdU=", "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="}, "node_modules/boom": {"version": "7.3.0", "resolved": "https://registry.npmmirror.com/boom/download/boom-7.3.0.tgz", "integrity": "sha1-czptlW0zsLGZnaP+bBKZaVDQF7k=", "deprecated": "This module has moved and is now available at @hapi/boom. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues.", "dependencies": {"hoek": "6.x.x"}}, "node_modules/boxen": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/boxen/download/boxen-5.1.2.tgz?cache=0&sync_timestamp=1634028552381&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fboxen%2Fdownload%2Fboxen-5.1.2.tgz", "integrity": "sha1-eIy2hvyDwfSG36ikDGj8K4MdK1A=", "dev": true, "dependencies": {"ansi-align": "^3.0.0", "camelcase": "^6.2.0", "chalk": "^4.1.0", "cli-boxes": "^2.2.1", "string-width": "^4.2.2", "type-fest": "^0.20.2", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/boxen/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/camelcase": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.0.tgz", "integrity": "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/boxen/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/boxen/node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/buffer-alloc": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz", "integrity": "sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=", "optional": true, "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz", "integrity": "sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=", "optional": true}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmmirror.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI=", "engines": {"node": "*"}}, "node_modules/buffer-fill": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/buffer-fill/download/buffer-fill-1.0.0.tgz", "integrity": "sha1-+PeLdniYiO858gXNY39o5wISKyw=", "optional": true}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.2.tgz?cache=0&sync_timestamp=1627578450949&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbuffer-from%2Fdownload%2Fbuffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="}, "node_modules/buffermaker": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/buffermaker/download/buffermaker-1.2.1.tgz", "integrity": "sha1-BjH5K4kahLdQ8QNkkayFfHNEKfQ=", "dependencies": {"long": "1.1.2"}}, "node_modules/buffers": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/buffers/download/buffers-0.1.1.tgz", "integrity": "sha1-skV5w77U1tOWru5tmorn9Ugqt7s=", "engines": {"node": ">=0.2.0"}}, "node_modules/builtin-modules": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/builtin-modules/download/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8=", "engines": {"node": ">=0.10.0"}}, "node_modules/busboy": {"version": "0.2.14", "resolved": "https://registry.npmmirror.com/busboy/-/busboy-0.2.14.tgz", "integrity": "sha512-InWFDomvlkEj+xWLBfU3AvnbVYqeTWmQopiW0tWWEy5yehYm2YkGEc59sUmw/4ty5Zj/b0WHGs1LgecuBSBGrg==", "dependencies": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "engines": {"node": ">=0.8.0"}}, "node_modules/busboy/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/busboy/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}, "node_modules/bytes": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY=", "engines": {"node": ">= 0.8"}}, "node_modules/cache-content-type": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/cache-content-type/download/cache-content-type-1.0.1.tgz", "integrity": "sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=", "dependencies": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/cacheable-request": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/cacheable-request/download/cacheable-request-6.1.0.tgz", "integrity": "sha1-IP+4vRYrpL4R6VZ9gj22UQUsqRI=", "dev": true, "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/get-stream/download/get-stream-5.2.0.tgz", "integrity": "sha1-SWaheV7lrOZecGxLe+txJX1uItM=", "dev": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz?cache=0&sync_timestamp=1634551808987&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-2.0.0.tgz", "integrity": "sha1-JgPni3tLAAbLyi+8yKMgJVislHk=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/cacheable-request/node_modules/pump": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz?cache=0&sync_timestamp=1610403020286&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-me-maybe": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms="}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "engines": {"node": ">=6"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="}, "node_modules/chai": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/chai/download/chai-4.3.4.tgz?cache=0&sync_timestamp=1624607982671&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchai%2Fdownload%2Fchai-4.3.4.tgz", "integrity": "sha1-tV5lWzHh6scJm+TAjCGWT84ubEk=", "dev": true, "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^3.0.1", "get-func-name": "^2.0.0", "pathval": "^1.1.1", "type-detect": "^4.0.5"}, "engines": {"node": ">=4"}}, "node_modules/chainsaw": {"version": "0.1.0", "resolved": "https://registry.npmmirror.com/chainsaw/download/chainsaw-0.1.0.tgz", "integrity": "sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=", "dependencies": {"traverse": ">=0.3.0 <0.4"}, "engines": {"node": "*"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-flag%2Fdownload%2Fhas-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/check-error": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/check-error/download/check-error-1.0.2.tgz", "integrity": "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII=", "dev": true, "engines": {"node": "*"}}, "node_modules/chokidar": {"version": "3.5.2", "resolved": "https://registry.npmmirror.com/chokidar/download/chokidar-3.5.2.tgz", "integrity": "sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=", "dev": true, "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chownr": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/chownr/download/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "optional": true}, "node_modules/ci-info": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=", "dev": true}, "node_modules/circular-json": {"version": "0.5.9", "resolved": "https://registry.npmmirror.com/circular-json/-/circular-json-0.5.9.tgz", "integrity": "sha512-4ivwqHpIFJZBuhN3g/pEcdbnGUywkBblloGbkglyloVjjR3uT6tieI89MVOfbP2tHX5sgb01FuLgAOzebNlJNQ==", "deprecated": "CircularJSON is in maintenance only, flatted is its successor."}, "node_modules/class-transformer": {"version": "0.2.3", "resolved": "https://registry.npmmirror.com/class-transformer/download/class-transformer-0.2.3.tgz", "integrity": "sha1-WYySynHcynP5HMuHXXSjhHzPoy0="}, "node_modules/class-validator": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/class-validator/download/class-validator-0.9.1.tgz", "integrity": "sha1-1g5YxdFKvKCkG844z3kq1MRtFTE=", "dependencies": {"google-libphonenumber": "^3.1.6", "validator": "10.4.0"}}, "node_modules/class-validator/node_modules/validator": {"version": "10.4.0", "resolved": "https://registry.npmmirror.com/validator/download/validator-10.4.0.tgz", "integrity": "sha1-7pmkSvs7te01ChWfBWynKiBM/Dw=", "engines": {"node": ">= 0.10"}}, "node_modules/cli-boxes": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/cli-boxes/download/cli-boxes-2.2.1.tgz", "integrity": "sha1-3dUDXSUJT84iDpyrQKRYQKRAMY8=", "dev": true, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cliui": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-4.1.0.tgz", "integrity": "sha1-NIQi2+gtgAswIu709qwQvy5NG0k=", "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/string-width": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/clone-response": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/clone-response/download/clone-response-1.0.2.tgz", "integrity": "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=", "dev": true, "dependencies": {"mimic-response": "^1.0.0"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://registry.npmmirror.com/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=", "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/co-body": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/co-body/download/co-body-5.2.0.tgz", "integrity": "sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=", "dependencies": {"inflation": "^2.0.0", "qs": "^6.4.0", "raw-body": "^2.2.0", "type-is": "^1.6.14"}}, "node_modules/code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/code-point-at/download/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "node_modules/combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886357672&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="}, "node_modules/component-emitter": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmmirror.com/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "engines": ["node >= 0.8"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/configstore": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/configstore/download/configstore-5.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconfigstore%2Fdownload%2Fconfigstore-5.0.1.tgz", "integrity": "sha1-02UCG130uYzdGH1qOw4/anzF7ZY=", "dev": true, "dependencies": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/console-control-strings/download/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "optional": true}, "node_modules/content-disposition": {"version": "0.5.3", "resolved": "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "engines": {"node": ">= 0.6"}}, "node_modules/cookiejar": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/cookiejar/download/cookiejar-2.1.3.tgz?cache=0&sync_timestamp=1632881937184&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcookiejar%2Fdownload%2Fcookiejar-2.1.3.tgz", "integrity": "sha1-/HpiFuQI50QUuQIwBQhC2s2nWsw=", "dev": true}, "node_modules/cookies": {"version": "0.8.0", "resolved": "https://registry.npmmirror.com/cookies/download/cookies-0.8.0.tgz", "integrity": "sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=", "dependencies": {"depd": "~2.0.0", "keygrip": "~1.1.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/core-js": {"version": "2.6.12", "resolved": "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz?cache=0&sync_timestamp=1635142905717&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcore-js%2Fdownload%2Fcore-js-2.6.12.tgz", "integrity": "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "hasInstallScript": true}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="}, "node_modules/cron": {"version": "1.8.2", "resolved": "https://registry.npmmirror.com/cron/download/cron-1.8.2.tgz", "integrity": "sha1-SsXjxVuowWPYTzQHvelGMtqDcM4=", "dependencies": {"moment-timezone": "^0.5.x"}}, "node_modules/cross-spawn": {"version": "6.0.5", "resolved": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/cross-spawn/node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "bin": {"semver": "bin/semver"}}, "node_modules/crypto-random-string": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/crypto-random-string/download/crypto-random-string-2.0.0.tgz", "integrity": "sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/date-format": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/date-format/download/date-format-2.1.0.tgz", "integrity": "sha1-MdW16iEc9f12TNOLr50DPffhJc8=", "deprecated": "2.x is no longer supported. Please upgrade to 4.x or higher.", "engines": {"node": ">=4.0"}}, "node_modules/debug": {"version": "4.3.2", "resolved": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055760479&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "engines": {"node": ">=0.10.0"}}, "node_modules/decompress-response": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/decompress-response/download/decompress-response-3.3.0.tgz", "integrity": "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=", "devOptional": true, "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/deep-eql": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/deep-eql/download/deep-eql-3.0.1.tgz", "integrity": "sha1-38lARACtHI/gI+faHfHBR8S0RN8=", "dev": true, "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=0.12"}}, "node_modules/deep-equal": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.0.1.tgz?cache=0&sync_timestamp=1606860754950&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "node_modules/deep-extend": {"version": "0.6.0", "resolved": "https://registry.npmmirror.com/deep-extend/download/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "devOptional": true, "engines": {"node": ">=4.0.0"}}, "node_modules/defer-to-connect": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/defer-to-connect/download/defer-to-connect-1.1.3.tgz", "integrity": "sha1-MxrgUMCNz3ifjIOnuB8O2U9KxZE=", "dev": true}, "node_modules/define-properties": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "dependencies": {"object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delegates/download/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="}, "node_modules/denque": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/denque/download/denque-1.5.1.tgz", "integrity": "sha1-B/Zw4pyaePj67LJWah4sEZKcXL8=", "engines": {"node": ">=0.10"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "node_modules/detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/detect-libc/download/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/dicer": {"version": "0.2.5", "resolved": "https://registry.npmmirror.com/dicer/-/dicer-0.2.5.tgz", "integrity": "sha512-FDvbtnq7dzlPz0wyYlOExifDEZcu8h+rErEXgfxqmLfRfC/kJidEFh4+effJRO3P0xmfqyPbSMG0LveNRfTKVg==", "dependencies": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "engines": {"node": ">=0.8.0"}}, "node_modules/dicer/node_modules/readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/dicer/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}, "node_modules/diff": {"version": "3.5.0", "resolved": "https://registry.npmmirror.com/diff/download/diff-3.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdiff%2Fdownload%2Fdiff-3.5.0.tgz", "integrity": "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=", "dev": true, "engines": {"node": ">=0.3.1"}}, "node_modules/dot-prop": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/dot-prop/download/dot-prop-5.3.0.tgz", "integrity": "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=", "dev": true, "dependencies": {"is-obj": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/double-ended-queue": {"version": "2.1.0-0", "resolved": "https://registry.npmmirror.com/double-ended-queue/download/double-ended-queue-2.1.0-0.tgz", "integrity": "sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw="}, "node_modules/duplexer3": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/duplexer3/download/duplexer3-0.1.4.tgz", "integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=", "dev": true}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "node_modules/ejs": {"version": "2.7.4", "resolved": "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz", "integrity": "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo=", "hasInstallScript": true, "engines": {"node": ">=0.10.0"}}, "node_modules/emitter-listener": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/emitter-listener/download/emitter-listener-1.1.2.tgz", "integrity": "sha1-VrFA6PaZI3Wz18ssqxzHQy2WMug=", "dependencies": {"shimmer": "^1.2.0"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632751333727&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "dependencies": {"once": "^1.4.0"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/escalade/download/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-goat": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/escape-goat/download/escape-goat-2.1.1.tgz", "integrity": "sha1-Gy3HcANnbEV+x2Cy3GjttkgYhnU=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "engines": {"node": ">=0.8.0"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/eventemitter3/download/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="}, "node_modules/execa": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/expand-template": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/expand-template/download/expand-template-2.0.3.tgz", "integrity": "sha1-bhSz/O4POmNA7LV9LokYaSBSpHw=", "optional": true, "engines": {"node": ">=6"}}, "node_modules/extend": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g==", "engines": ["node >=0.6.0"]}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "optional": true}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1633618631704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/flatted": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/flatted/download/flatted-2.0.2.tgz?cache=0&sync_timestamp=1627541315228&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg="}, "node_modules/follow-redirects": {"version": "1.14.4", "resolved": "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.4.tgz?cache=0&sync_timestamp=1631622129411&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.14.4.tgz", "integrity": "sha1-g4/fSKi73XnlLuUfsclOPtmLk3k=", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw==", "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/form-data/download/form-data-2.5.1.tgz", "integrity": "sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ=", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/format-util": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/format-util/download/format-util-1.0.5.tgz", "integrity": "sha1-H/tFDIoD57zP/kBkMYCRjMKX0nE="}, "node_modules/formidable": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/formidable/download/formidable-1.2.2.tgz", "integrity": "sha1-v2muopcpgmdfAIZTQrmCmG9rjdk=", "deprecated": "Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau", "funding": {"url": "https://ko-fi.com/tunnckoCore/commissions"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "engines": {"node": ">= 0.6"}}, "node_modules/fs-constants": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/fs-constants/download/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "optional": true}, "node_modules/fs-extra": {"version": "7.0.1", "resolved": "https://registry.npmmirror.com/fs-extra/download/fs-extra-7.0.1.tgz", "integrity": "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=", "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/fsevents/download/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "node_modules/gauge": {"version": "2.7.4", "resolved": "https://registry.npmmirror.com/gauge/download/gauge-2.7.4.tgz?cache=0&sync_timestamp=1627307653902&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fgauge%2Fdownload%2Fgauge-2.7.4.tgz", "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "deprecated": "This package is no longer supported.", "optional": true, "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/get-caller-file": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-1.0.3.tgz", "integrity": "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o="}, "node_modules/get-func-name": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/get-func-name/download/get-func-name-2.0.0.tgz?cache=0&sync_timestamp=1624607980603&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fget-func-name%2Fdownload%2Fget-func-name-2.0.0.tgz", "integrity": "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE=", "dev": true, "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz", "integrity": "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-port": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/get-port/download/get-port-3.2.0.tgz?cache=0&sync_timestamp=1633281808408&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fget-port%2Fdownload%2Fget-port-3.2.0.tgz", "integrity": "sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/get-stream/node_modules/pump": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/github-from-package": {"version": "0.0.0", "resolved": "https://registry.npmmirror.com/github-from-package/download/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=", "optional": true}, "node_modules/glob": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "integrity": "sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=", "deprecated": "Glob versions prior to v9 are no longer supported", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/global-dirs": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/global-dirs/download/global-dirs-3.0.0.tgz", "integrity": "sha1-cKdv6E6jFas3sfVXbL3n1I73JoY=", "dev": true, "dependencies": {"ini": "2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/global-dirs/node_modules/ini": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ini/download/ini-2.0.0.tgz", "integrity": "sha1-5f1Vbs3VcmvpePoQAYYurLCpS8U=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/google-libphonenumber": {"version": "3.2.25", "resolved": "https://registry.npmmirror.com/google-libphonenumber/download/google-libphonenumber-3.2.25.tgz", "integrity": "sha1-O8GgQfo5JznSAerPZnYd8jbxYUs=", "engines": {"node": ">=0.10"}}, "node_modules/got": {"version": "9.6.0", "resolved": "https://registry.npmmirror.com/got/download/got-9.6.0.tgz", "integrity": "sha1-7fRefWf5lUVwXeH3u+7rEhdl7YU=", "dev": true, "dependencies": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "engines": {"node": ">=8.6"}}, "node_modules/graceful-fs": {"version": "4.2.8", "resolved": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.8.tgz", "integrity": "sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo="}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q==", "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.5", "resolved": "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "deprecated": "this library is no longer supported", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-flag%2Fdownload%2Fhas-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz?cache=0&sync_timestamp=1614443557459&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-symbols%2Fdownload%2Fhas-symbols-1.0.2.tgz", "integrity": "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM=", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz", "integrity": "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/has-unicode/download/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "optional": true}, "node_modules/has-yarn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/has-yarn/download/has-yarn-2.1.0.tgz?cache=0&sync_timestamp=1631298711761&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-yarn%2Fdownload%2Fhas-yarn-2.1.0.tgz", "integrity": "sha1-E34RNUp7W/EapctknPDG8/8rLnc=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/hoek": {"version": "6.1.3", "resolved": "https://registry.npmmirror.com/hoek/download/hoek-6.1.3.tgz", "integrity": "sha1-c7fTOVLgH+J6OLBFcpS3ndjaJCw=", "deprecated": "This module has moved and is now available at @hapi/hoek. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues."}, "node_modules/hosted-git-info": {"version": "2.8.9", "resolved": "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=", "dev": true}, "node_modules/http-assert": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/http-assert/download/http-assert-1.5.0.tgz", "integrity": "sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=", "dependencies": {"deep-equal": "~1.0.1", "http-errors": "~1.8.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-cache-semantics": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/http-cache-semantics/download/http-cache-semantics-4.1.0.tgz", "integrity": "sha1-SekcXL82yblLz81xwj1SSex045A=", "dev": true}, "node_modules/http-errors": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.8.0.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.8.0.tgz", "integrity": "sha1-ddG75JfhBE9R5O6ecEpi8o0zZQc=", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-errors/node_modules/depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "engines": {"node": ">= 0.6"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmmirror.com/http-proxy/download/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore-by-default": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk=", "dev": true}, "node_modules/import-lazy": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/import-lazy/download/import-lazy-2.1.0.tgz", "integrity": "sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/inflation": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/inflation/download/inflation-2.0.0.tgz", "integrity": "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8=", "engines": {"node": ">= 0.8.0"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "node_modules/ini": {"version": "1.3.8", "resolved": "https://registry.npmmirror.com/ini/download/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "devOptional": true}, "node_modules/invert-kv": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/invert-kv/download/invert-kv-2.0.0.tgz", "integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=", "engines": {"node": ">=4"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-buffer": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/is-buffer/download/is-buffer-2.0.5.tgz?cache=0&sync_timestamp=1604429876103&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-buffer%2Fdownload%2Fis-buffer-2.0.5.tgz", "integrity": "sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "engines": {"node": ">=4"}}, "node_modules/is-ci": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1618847026826&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz", "integrity": "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=", "dev": true, "dependencies": {"ci-info": "^2.0.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-core-module": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz", "integrity": "sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-generator-function": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/is-generator-function/download/is-generator-function-1.0.10.tgz", "integrity": "sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-installed-globally": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/is-installed-globally/download/is-installed-globally-0.4.0.tgz", "integrity": "sha1-mg/UB5ScMPhutpWe8beZTtC3tSA=", "dev": true, "dependencies": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-npm": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/is-npm/download/is-npm-5.0.0.tgz", "integrity": "sha1-Q+jWXMVuG2f41HJiz2ZwmRk/Rag=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-obj": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-obj/download/is-obj-2.0.0.tgz", "integrity": "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-3.0.3.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "engines": {"node": ">=0.10.0"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "node_modules/is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/is-utf8/download/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=", "dev": true}, "node_modules/is-yarn-global": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/is-yarn-global/download/is-yarn-global-0.3.0.tgz", "integrity": "sha1-1QLTOCWQ6jAEiTdGdUyJE5lz4jI=", "dev": true}, "node_modules/isarray": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "node_modules/isemail": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/isemail/download/isemail-3.2.0.tgz", "integrity": "sha1-WTEKAhkxqfsGu7UeFVzgs/I2gyw=", "dependencies": {"punycode": "2.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "node_modules/isstream": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="}, "node_modules/jasmine": {"version": "3.10.0", "resolved": "https://registry.npmmirror.com/jasmine/download/jasmine-3.10.0.tgz", "integrity": "sha1-rNPNVgqdINj9rWvS3QWGfRiFA/M=", "dev": true, "dependencies": {"glob": "^7.1.6", "jasmine-core": "~3.10.0"}, "bin": {"jasmine": "bin/jasmine.js"}}, "node_modules/jasmine-core": {"version": "3.10.0", "resolved": "https://registry.npmmirror.com/jasmine-core/download/jasmine-core-3.10.0.tgz", "integrity": "sha1-Hj5AU9lU620L+7PrhZuyGlZV3kU=", "dev": true}, "node_modules/jasmine-ts": {"version": "0.3.3", "resolved": "https://registry.npmmirror.com/jasmine-ts/download/jasmine-ts-0.3.3.tgz", "integrity": "sha1-ommQtTN5yU33Bq3RWIdZP0ADKXQ=", "dev": true, "dependencies": {"yargs": "^16.2.0"}, "bin": {"jasmine-ts": "lib/index.js"}, "peerDependencies": {"jasmine": ">=3.4", "ts-node": ">=3.2.0 <=10", "typescript": ">=3.5.2"}}, "node_modules/jasmine-ts/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/jasmine-ts/node_modules/cliui": {"version": "7.0.4", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-7.0.4.tgz", "integrity": "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=", "dev": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/jasmine-ts/node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/jasmine-ts/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/jasmine-ts/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/jasmine-ts/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/jasmine-ts/node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/jasmine-ts/node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/jasmine-ts/node_modules/yargs": {"version": "16.2.0", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-16.2.0.tgz", "integrity": "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=", "dev": true, "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/jasmine-ts/node_modules/yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz", "integrity": "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=", "dev": true, "engines": {"node": ">=10"}}, "node_modules/joi": {"version": "14.3.1", "resolved": "https://registry.npmmirror.com/joi/download/joi-14.3.1.tgz", "integrity": "sha1-FkomLsC4VUZuDDXuoqiFrotscDw=", "deprecated": "This module has moved and is now available at @hapi/joi. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues.", "dependencies": {"hoek": "6.x.x", "isemail": "3.x.x", "topo": "3.x.x"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}, "node_modules/json-buffer": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/json-buffer/download/json-buffer-3.0.0.tgz", "integrity": "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg=", "dev": true}, "node_modules/json-schema": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "node_modules/json-schema-ref-parser": {"version": "5.1.3", "resolved": "https://registry.npmmirror.com/json-schema-ref-parser/download/json-schema-ref-parser-5.1.3.tgz", "integrity": "sha1-+GxYaPQImOaRaeG7yFRyWk/Q4a0=", "deprecated": "Please switch to @apidevtools/json-schema-ref-parser", "dependencies": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "js-yaml": "^3.12.0", "ono": "^4.0.6"}}, "node_modules/json-schema-ref-parser/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dependencies": {"ms": "^2.1.1"}}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "node_modules/json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "dependencies": {"jsonify": "~0.0.0"}}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "node_modules/jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz?cache=0&sync_timestamp=1604161876665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjsonfile%2Fdownload%2Fjsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonify": {"version": "0.0.0", "resolved": "https://registry.npmmirror.com/jsonify/download/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "engines": {"node": "*"}}, "node_modules/jsonschema": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/jsonschema/download/jsonschema-1.2.4.tgz", "integrity": "sha1-pGusXTUGolRGW8VIh24mfG0NZGQ=", "engines": {"node": "*"}}, "node_modules/jsonschema-draft4": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/jsonschema-draft4/download/jsonschema-draft4-1.0.0.tgz", "integrity": "sha1-8K8gBQVPDwrefqIRhhS2ncUS2GU="}, "node_modules/jsprim": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "node_modules/kafka-node": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/kafka-node/download/kafka-node-4.1.3.tgz", "integrity": "sha1-nl/fOlViNw/w+CxUgb0UpuOa2T0=", "dependencies": {"async": "^2.6.2", "binary": "~0.3.0", "bl": "^2.2.0", "buffer-crc32": "~0.2.5", "buffermaker": "~1.2.0", "debug": "^2.1.3", "denque": "^1.3.0", "lodash": "^4.17.4", "minimatch": "^3.0.2", "nested-error-stacks": "^2.0.0", "optional": "^0.1.3", "retry": "^0.10.1", "uuid": "^3.0.0"}, "engines": {"node": ">=6.4.0"}, "optionalDependencies": {"snappy": "^6.0.1"}}, "node_modules/kafka-node/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dependencies": {"ms": "2.0.0"}}, "node_modules/kafka-node/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/keygrip": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/keygrip/download/keygrip-1.1.0.tgz", "integrity": "sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=", "dependencies": {"tsscmp": "1.0.6"}, "engines": {"node": ">= 0.6"}}, "node_modules/keyv": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/keyv/download/keyv-3.1.0.tgz", "integrity": "sha1-7MIoSG9pmR5J6UdkhaW+Ho/FxNk=", "dev": true, "dependencies": {"json-buffer": "3.0.0"}}, "node_modules/koa": {"version": "2.13.4", "resolved": "https://registry.npmmirror.com/koa/download/koa-2.13.4.tgz?cache=0&sync_timestamp=1634623957508&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fkoa%2Fdownload%2Fkoa-2.13.4.tgz", "integrity": "sha1-7lsMs54LgGnDjRFROcd0gz0yRi4=", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.8.0", "debug": "^4.3.2", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}}, "node_modules/koa-body": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/koa-body/download/koa-body-4.2.0.tgz", "integrity": "sha1-NyKSCLggdhrKWCLRTF/FXO4xsm8=", "dependencies": {"@types/formidable": "^1.0.31", "co-body": "^5.1.1", "formidable": "^1.1.1"}}, "node_modules/koa-compose": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/koa-compose/download/koa-compose-4.1.0.tgz", "integrity": "sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc="}, "node_modules/koa-convert": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/koa-convert/download/koa-convert-2.0.0.tgz", "integrity": "sha1-hqDETYHUBVG64i/uZwmQRXPupPU=", "dependencies": {"co": "^4.6.0", "koa-compose": "^4.1.0"}, "engines": {"node": ">= 10"}}, "node_modules/koa-ejs": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/koa-ejs/download/koa-ejs-4.3.0.tgz", "integrity": "sha1-BthFmx1S9MQbQT2HePceGrLsUAk=", "dependencies": {"debug": "^2.6.1", "ejs": "^2.6.1", "mz": "^2.6.0"}}, "node_modules/koa-ejs/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dependencies": {"ms": "2.0.0"}}, "node_modules/koa-ejs/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/koa-etag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/koa-etag/download/koa-etag-3.0.0.tgz", "integrity": "sha1-nvc4Ld1agqsN6xU0FckVg293HT8=", "dependencies": {"etag": "^1.3.0", "mz": "^2.1.0"}}, "node_modules/koa-multer": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/koa-multer/-/koa-multer-1.0.2.tgz", "integrity": "sha512-0kFzN4atVd+9oiG+4fYxQ9S2T3dPhKNvmhITIY606Qn9wLEmfhW0DhSpOzRYhddN//4rh/TCK95TMtflmFa5lA==", "deprecated": "Please use @koa/multer instead, see <https://github.com/koajs/multer>", "dependencies": {"multer": "1.3.0"}, "engines": {"node": ">= 4"}}, "node_modules/koa-router": {"version": "7.4.0", "resolved": "https://registry.npmmirror.com/koa-router/download/koa-router-7.4.0.tgz?cache=0&sync_timestamp=1629648499991&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fkoa-router%2Fdownload%2Fkoa-router-7.4.0.tgz", "integrity": "sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=", "deprecated": "**IMPORTANT 10x+ PERFORMANCE UPGRADE**: Please upgrade to v12.0.1+ as we have fixed an issue with debuglog causing 10x slower router benchmark performance, see https://github.com/koajs/router/pull/173", "dependencies": {"debug": "^3.1.0", "http-errors": "^1.3.1", "koa-compose": "^3.0.0", "methods": "^1.0.1", "path-to-regexp": "^1.1.1", "urijs": "^1.19.0"}, "engines": {"node": ">= 4"}}, "node_modules/koa-router/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dependencies": {"ms": "^2.1.1"}}, "node_modules/koa-router/node_modules/koa-compose": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/koa-compose/download/koa-compose-3.2.1.tgz", "integrity": "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=", "dependencies": {"any-promise": "^1.1.0"}}, "node_modules/koa-send": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/koa-send/download/koa-send-4.1.3.tgz", "integrity": "sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=", "dependencies": {"debug": "^2.6.3", "http-errors": "^1.6.1", "mz": "^2.6.0", "resolve-path": "^1.4.0"}, "engines": {"node": ">= 7.6.0"}}, "node_modules/koa-send/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dependencies": {"ms": "2.0.0"}}, "node_modules/koa-send/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/latest-version": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/latest-version/download/latest-version-5.1.0.tgz", "integrity": "sha1-EZ3+kI/jjRXfpD7NE/oS7Igy+s4=", "dev": true, "dependencies": {"package-json": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/lcid": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/lcid/download/lcid-2.0.0.tgz", "integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "dependencies": {"invert-kv": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/load-json-file": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/load-json-file/download/load-json-file-1.1.0.tgz?cache=0&sync_timestamp=1631508525141&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fload-json-file%2Fdownload%2Fload-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/locate-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1618847150612&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "node_modules/lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmmirror.com/lodash.get/download/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk=", "deprecated": "This package is deprecated. Use the optional chaining (?.) operator instead."}, "node_modules/lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA=", "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead."}, "node_modules/log4js": {"version": "4.5.1", "resolved": "https://registry.npmmirror.com/log4js/download/log4js-4.5.1.tgz", "integrity": "sha1-5UNiXpfZ5vPm58n8GW3WqyyuMLU=", "deprecated": "4.x is no longer supported. Please upgrade to 6.x or higher.", "dependencies": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.4", "streamroller": "^1.0.6"}, "engines": {"node": ">=6.0"}}, "node_modules/long": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/long/download/long-1.1.2.tgz", "integrity": "sha1-6u9ZUcp1UdlpJrgtokLbnWso+1M=", "engines": {"node": ">=0.6"}}, "node_modules/lowercase-keys": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz?cache=0&sync_timestamp=1634551808987&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-1.0.1.tgz", "integrity": "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/lru-cache": {"version": "4.1.5", "resolved": "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/make-dir": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://registry.npmmirror.com/make-error/download/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "dev": true}, "node_modules/map-age-cleaner": {"version": "0.1.3", "resolved": "https://registry.npmmirror.com/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz?cache=0&sync_timestamp=1629750638543&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmap-age-cleaner%2Fdownload%2Fmap-age-cleaner-0.1.3.tgz", "integrity": "sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=", "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "engines": {"node": ">= 0.6"}}, "node_modules/mem": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/mem/download/mem-4.3.0.tgz", "integrity": "sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=", "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "engines": {"node": ">= 0.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.50.0", "resolved": "https://registry.npmmirror.com/mime-db/download/mime-db-1.50.0.tgz?cache=0&sync_timestamp=1631863129751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.50.0.tgz", "integrity": "sha1-q9SslOmNPA4YUBbGerRdX95AwR8=", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.33", "resolved": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108241037&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz", "integrity": "sha1-H6EqkERy+v0GjkjZ6EAfdNP3Dts=", "dependencies": {"mime-db": "1.50.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=", "engines": {"node": ">=6"}}, "node_modules/mimic-response": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/mimic-response/download/mimic-response-1.0.1.tgz", "integrity": "sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=", "devOptional": true, "engines": {"node": ">=4"}}, "node_modules/minimatch": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.5", "resolved": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "node_modules/mkdirp": {"version": "0.5.5", "resolved": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "dependencies": {"minimist": "^1.2.5"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/moment": {"version": "2.29.1", "resolved": "https://registry.npmmirror.com/moment/download/moment-2.29.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmoment%2Fdownload%2Fmoment-2.29.1.tgz", "integrity": "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M=", "engines": {"node": "*"}}, "node_modules/moment-timezone": {"version": "0.5.33", "resolved": "https://registry.npmmirror.com/moment-timezone/download/moment-timezone-0.5.33.tgz", "integrity": "sha1-slL9a7V/NBybWaWrYajlGnO70iw=", "dependencies": {"moment": ">= 2.9.0"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}, "node_modules/multer": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/multer/-/multer-1.3.0.tgz", "integrity": "sha512-wbAkTsh0QXkvqvHCU2qSLEXLuRN7IKMEe80+JrXfJzANniPNgrNcDOMKfGgR1EhL7y7MHIbODVwT7uaVY20ggw==", "deprecated": "Multer 1.x is affected by CVE-2022-24434. This is fixed in v1.4.4-lts.1 which drops support for versions of Node.js before 6. Please upgrade to at least Node.js 6 and version 1.4.4-lts.1 of Multer. If you need support for older versions of Node.js, we are open to accepting patches that would fix the CVE on the main 1.x release line, whilst maintaining compatibility with Node.js 0.10.", "dependencies": {"append-field": "^0.1.0", "busboy": "^0.2.11", "concat-stream": "^1.5.0", "mkdirp": "^0.5.1", "object-assign": "^3.0.0", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/multer/node_modules/object-assign": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha512-jHP15vXVGeVh1HuaA2wY6lxk+whK/x4KBG88VXeRma7CCun7iGD5qPc4eYykQ9sdQvg8jkwFKsSxHln2ybW3xQ==", "engines": {"node": ">=0.10.0"}}, "node_modules/mz": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "node_modules/nan": {"version": "2.15.0", "resolved": "https://registry.npmmirror.com/nan/download/nan-2.15.0.tgz", "integrity": "sha1-PzSkc/8Y4VwbVia2KQO1rW5mX+4=", "optional": true}, "node_modules/napi-build-utils": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/napi-build-utils/download/napi-build-utils-1.0.2.tgz", "integrity": "sha1-sf3cCyxG44Cgt6dvmE3UfEGhOAY=", "optional": true}, "node_modules/negotiator": {"version": "0.6.2", "resolved": "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs=", "engines": {"node": ">= 0.6"}}, "node_modules/nested-error-stacks": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/nested-error-stacks/download/nested-error-stacks-2.1.0.tgz", "integrity": "sha1-D73PPhP+SZR4EoBST4uWsM3/nGE="}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="}, "node_modules/node-abi": {"version": "2.30.1", "resolved": "https://registry.npmmirror.com/node-abi/download/node-abi-2.30.1.tgz?cache=0&sync_timestamp=1632784314415&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-abi%2Fdownload%2Fnode-abi-2.30.1.tgz", "integrity": "sha1-xDfUsf4OKFqvKQ1FtF1Nev7axM8=", "optional": true, "dependencies": {"semver": "^5.4.1"}}, "node_modules/node-abi/node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/node-fetch": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.3.tgz", "integrity": "sha512-aru9GLSvU8scV9SMrOa0AiPF8Rst8mSMaN51GGxMlTN3yL7zybAYjHSVefVOmurcGj2pdF0F1Qy8Nekzqbk+/Q==", "dependencies": {"encoding": "^0.1.11"}}, "node_modules/node-object-hash": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/node-object-hash/download/node-object-hash-1.4.2.tgz?cache=0&sync_timestamp=1631619359445&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-object-hash%2Fdownload%2Fnode-object-hash-1.4.2.tgz", "integrity": "sha1-OFgz2FsimQK3WCYiT2B3vpaanpQ=", "engines": {"node": ">=0.10.0"}}, "node_modules/nodemon": {"version": "2.0.14", "resolved": "https://registry.npmmirror.com/nodemon/download/nodemon-2.0.14.tgz?cache=0&sync_timestamp=1634655502807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnodemon%2Fdownload%2Fnodemon-2.0.14.tgz", "integrity": "sha1-KHx6L2zYoYsH6UzXduy2qC5LpDk=", "dev": true, "hasInstallScript": true, "dependencies": {"chokidar": "^3.2.2", "debug": "^3.2.6", "ignore-by-default": "^1.0.1", "minimatch": "^3.0.4", "pstree.remy": "^1.1.7", "semver": "^5.7.1", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.3", "update-notifier": "^5.1.0"}, "bin": {"nodemon": "bin/nodemon.js"}, "engines": {"node": ">=8.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/nodemon"}}, "node_modules/nodemon/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/nodemon/node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/noop-logger": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/noop-logger/download/noop-logger-0.1.1.tgz", "integrity": "sha1-lKKxYzxPExdVMAfYlm/Q6EG2pMI=", "optional": true}, "node_modules/nopt": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/nopt/download/nopt-1.0.10.tgz?cache=0&sync_timestamp=1624607881839&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnopt%2Fdownload%2Fnopt-1.0.10.tgz", "integrity": "sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=", "dev": true, "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "*"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "resolved": "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1629301911873&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-package-data/node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "4.5.1", "resolved": "https://registry.npmmirror.com/normalize-url/download/normalize-url-4.5.1.tgz", "integrity": "sha1-DdkM8SiO4dExO4cIHJpZMu5IUYo=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/nos-node-sdk": {"version": "0.0.4", "resolved": "https://registry.npmmirror.com/nos-node-sdk/-/nos-node-sdk-0.0.4.tgz", "integrity": "sha512-Jw3yzGfNscuIuigFWNawI/2KD4UcxJlm/2hr3BEeVD9709WkadTDFc2a+7HyEKbLU9TnEWR/CLqcbCCjI3Ciyw==", "dependencies": {"node-fetch": "~1.3.3", "q": "1.4.1", "superagent": "~1.4.0", "validator": "5.5.0", "xml2js": "~0.4.17"}}, "node_modules/nos-node-sdk/node_modules/async": {"version": "0.9.2", "resolved": "https://registry.npmmirror.com/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw=="}, "node_modules/nos-node-sdk/node_modules/combined-stream": {"version": "0.0.7", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==", "dependencies": {"delayed-stream": "0.0.5"}, "engines": {"node": ">= 0.8"}}, "node_modules/nos-node-sdk/node_modules/component-emitter": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.1.2.tgz", "integrity": "sha512-YhIbp3PJiznERfjlIkK0ue4obZxt2S60+0W8z24ZymOHT8sHloOqWOqZRU2eN5OlY8U08VFsP02letcu26FilA=="}, "node_modules/nos-node-sdk/node_modules/cookiejar": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/cookiejar/-/cookiejar-2.0.1.tgz", "integrity": "sha512-Txnl7P7okmx/FyZNRAjPyHMKISV2ADNbd+xITouEVyl2jUczrU4tJT40KcfQL/ifCo0kqqLgD49QlNofAAmBKQ=="}, "node_modules/nos-node-sdk/node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/nos-node-sdk/node_modules/delayed-stream": {"version": "0.0.5", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA==", "engines": {"node": ">=0.4.0"}}, "node_modules/nos-node-sdk/node_modules/extend": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/extend/-/extend-1.2.1.tgz", "integrity": "sha512-2/JwIYRpMBDSjbQjUUppNSrmc719crhFaWIdT+TRSVA8gE+6HEobQWqJ6VkPt/H8twS7h/0WWs7veh8wmp98Ng=="}, "node_modules/nos-node-sdk/node_modules/form-data": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-0.2.0.tgz", "integrity": "sha512-LkinaG6JazVhYj2AKi67NOIAhqXcBOQACraT0WdhWW4ZO3kTiS0X7C1nJ1jFZf6wak4bVHIA/oOzWkh2ThAipg==", "dependencies": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime-types": "~2.0.3"}, "engines": {"node": ">= 0.8"}}, "node_modules/nos-node-sdk/node_modules/formidable": {"version": "1.0.14", "resolved": "https://registry.npmmirror.com/formidable/-/formidable-1.0.14.tgz", "integrity": "sha512-aOskFHEfYwkSKSzGui5jhQ+uyLo2NTwpzhndggz2YZHlv0HkAi+zG5ZEBCL3GTvqLyr/FzX9Mvx9DueCmu2HzQ==", "deprecated": "Please upgrade to latest, formidable@v2 or formidable@v3! Check these notes: https://bit.ly/2ZEqIau", "engines": {"node": ">=0.8.0"}}, "node_modules/nos-node-sdk/node_modules/methods": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/methods/-/methods-1.0.1.tgz", "integrity": "sha512-2403MfnVypWSNIEpmQ26/ObZ5kSUx37E8NHRvriw0+I8Sne7k0HGuLGCk0OrCqURh4UIygD0cSsYq+Ll+kzNqA=="}, "node_modules/nos-node-sdk/node_modules/mime": {"version": "1.3.4", "resolved": "https://registry.npmmirror.com/mime/-/mime-1.3.4.tgz", "integrity": "sha512-sAaYXszED5ALBt665F0wMQCUXpGuZsGdopoqcHPdL39ZYdi7uHoZlhrfZfhv8WzivhBzr/oXwaj+yiK5wY8MXQ==", "bin": {"mime": "cli.js"}}, "node_modules/nos-node-sdk/node_modules/mime-db": {"version": "1.12.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.12.0.tgz", "integrity": "sha512-5aMAW7I4jZoZB27fXRuekqc4DVvJ7+hM8UcWrNj2mqibE54gXgPSonBYBdQW5hyaVNGmiYjY0ZMqn9fBefWYvA==", "engines": {"node": ">= 0.6"}}, "node_modules/nos-node-sdk/node_modules/mime-types": {"version": "2.0.14", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.14.tgz", "integrity": "sha512-2ZHUEstNkIf2oTWgtODr6X0Cc4Ns/RN/hktdozndiEhhAC2wxXejF1FH0XLHTEImE9h6gr/tcnr3YOnSGsxc7Q==", "dependencies": {"mime-db": "~1.12.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/nos-node-sdk/node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "node_modules/nos-node-sdk/node_modules/qs": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/qs/-/qs-2.3.3.tgz", "integrity": "sha512-f5M0HQqZWkzU8GELTY8LyMrGkr3bPjKoFtTkwUEqJQbcljbeK8M7mliP9Ia2xoOI6oMerp+QPS7oYJtpGmWe/A=="}, "node_modules/nos-node-sdk/node_modules/readable-stream": {"version": "1.0.27-1", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.0.27-1.tgz", "integrity": "sha512-uQE31HGhpMrqZwtDjRliOs2aC3XBi+DdkhLs+Xa0dvVD5eDiZr3+k8rKVZcyTzxosgtMw7B/twQsK3P1KTZeVg==", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "node_modules/nos-node-sdk/node_modules/string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}, "node_modules/nos-node-sdk/node_modules/superagent": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/superagent/-/superagent-1.4.0.tgz", "integrity": "sha512-OxU2FOnlgbjhRP0jvRAOz3E9p0FYxT+zkkBL81d9HwZ9VAMUR1JAnQFFNHroRnh3royOkqraV1adJ2J99VDC3w==", "deprecated": "Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net", "dependencies": {"component-emitter": "1.1.2", "cookiejar": "2.0.1", "debug": "2", "extend": "1.2.1", "form-data": "0.2.0", "formidable": "1.0.14", "methods": "1.0.1", "mime": "1.3.4", "qs": "2.3.3", "readable-stream": "1.0.27-1", "reduce-component": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/nos-node-sdk/node_modules/validator": {"version": "5.5.0", "resolved": "https://registry.npmmirror.com/validator/-/validator-5.5.0.tgz", "integrity": "sha512-r1y27o0tNMjLbGSjjiX2xTdxKT1HV/hO0iwy5yN/du3jT68r8sXr047p7bwh8jv0YaIX/12a2+D9y8XfRD8VIw==", "engines": {"node": ">= 0.10"}}, "node_modules/npm-run-path": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz?cache=0&sync_timestamp=1633420549182&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npmlog": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/npmlog/download/npmlog-4.1.2.tgz", "integrity": "sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=", "deprecated": "This package is no longer supported.", "optional": true, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/number-is-nan/download/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "engines": {"node": ">=0.10.0"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&sync_timestamp=1618847240432&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.11.0", "resolved": "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.0.tgz", "integrity": "sha1-nc6xRs7dQUig2eUauI00z1CZIrE=", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "dev": true, "dependencies": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dependencies": {"wrappy": "1"}}, "node_modules/only": {"version": "0.0.2", "resolved": "https://registry.npmmirror.com/only/download/only-0.0.2.tgz", "integrity": "sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q="}, "node_modules/ono": {"version": "4.0.11", "resolved": "https://registry.npmmirror.com/ono/download/ono-4.0.11.tgz", "integrity": "sha1-x/Qgmz45bopE70O5ztx/XXkdIh0=", "dependencies": {"format-util": "^1.0.3"}}, "node_modules/openapi-schema-validation": {"version": "0.4.2", "resolved": "https://registry.npmmirror.com/openapi-schema-validation/download/openapi-schema-validation-0.4.2.tgz", "integrity": "sha1-iVwpAhvgLgAPccUfhZ2lIRjrHiE=", "dependencies": {"jsonschema": "1.2.4", "jsonschema-draft4": "^1.0.0", "swagger-schema-official": "2.0.0-bab6bed"}}, "node_modules/optional": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/optional/download/optional-0.1.4.tgz", "integrity": "sha1-zbGpvtxzfSAl9pDO61DgSURP1bM="}, "node_modules/os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/os-homedir/download/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/os-locale": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/os-locale/download/os-locale-3.1.0.tgz?cache=0&sync_timestamp=1633618411079&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-3.1.0.tgz", "integrity": "sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=", "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-cancelable": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/p-cancelable/download/p-cancelable-1.1.0.tgz", "integrity": "sha1-0HjRWjr0CSIMiG8dmgyi5EGrJsw=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/p-defer": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/p-defer/download/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=", "engines": {"node": ">=4"}}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz?cache=0&sync_timestamp=1617947695861&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-finally%2Fdownload%2Fp-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "engines": {"node": ">=4"}}, "node_modules/p-is-promise": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/p-is-promise/download/p-is-promise-2.1.0.tgz", "integrity": "sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4=", "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz?cache=0&sync_timestamp=1629892721671&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-locate%2Fdownload%2Fp-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "engines": {"node": ">=6"}}, "node_modules/package-json": {"version": "6.5.0", "resolved": "https://registry.npmmirror.com/package-json/download/package-json-6.5.0.tgz", "integrity": "sha1-b+7ayjXnVyWHbQsOZJdGl/7RRbA=", "dev": true, "dependencies": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "engines": {"node": ">=8"}}, "node_modules/parse-json": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/parse-json/download/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "dependencies": {"error-ex": "^1.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "node_modules/path-to-regexp": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz?cache=0&sync_timestamp=1618847046445&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-1.8.0.tgz", "integrity": "sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=", "deprecated": "[WARNING] Use 1.9.0 instead of 1.8.0, reason: https://github.com/pillarjs/path-to-regexp/security/advisories/GHSA-9wv6-86v2-598j", "dependencies": {"isarray": "0.0.1"}}, "node_modules/path-type": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/path-type/download/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pathval": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/pathval/download/pathval-1.1.1.tgz", "integrity": "sha1-hTTnenfOesWiUS6iHg/bj89sPY0=", "dev": true, "engines": {"node": "*"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="}, "node_modules/picomatch": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/picomatch/download/picomatch-2.3.0.tgz?cache=0&sync_timestamp=1621648246651&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicomatch%2Fdownload%2Fpicomatch-2.3.0.tgz", "integrity": "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/pinkie/download/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/prebuild-install": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/prebuild-install/download/prebuild-install-5.3.0.tgz?cache=0&sync_timestamp=1628702746955&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fprebuild-install%2Fdownload%2Fprebuild-install-5.3.0.tgz", "integrity": "sha1-WLTYNE4DWQmQkx7giN1UAbAwBMg=", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "napi-build-utils": "^1.0.1", "node-abi": "^2.7.0", "noop-logger": "^0.1.1", "npmlog": "^4.0.1", "os-homedir": "^1.0.1", "pump": "^2.0.1", "rc": "^1.2.7", "simple-get": "^2.7.0", "tar-fs": "^1.13.0", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0"}, "bin": {"prebuild-install": "bin.js"}, "engines": {"node": ">=6"}}, "node_modules/prepend-http": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/prepend-http/download/prepend-http-2.0.0.tgz?cache=0&sync_timestamp=1628547565904&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fprepend-http%2Fdownload%2Fprepend-http-2.0.0.tgz", "integrity": "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "node_modules/pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="}, "node_modules/psl": {"version": "1.9.0", "resolved": "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz", "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="}, "node_modules/pstree.remy": {"version": "1.1.8", "resolved": "https://registry.npmmirror.com/pstree.remy/download/pstree.remy-1.1.8.tgz", "integrity": "sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=", "dev": true}, "node_modules/pump": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/pump/download/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "engines": {"node": ">=6"}}, "node_modules/pupa": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/pupa/download/pupa-2.1.1.tgz", "integrity": "sha1-9ej9SvwsXZeCj6pSNUnth0SiDWI=", "dev": true, "dependencies": {"escape-goat": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/q": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/q/-/q-1.4.1.tgz", "integrity": "sha512-/CdEdaw49VZVmyIDGUQKDDT53c7qBkO6g5CefWz91Ae+l4+cRtcDYwMTXh6me4O8TMldeGHG3N2Bl84V78Ywbg==", "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "6.10.1", "resolved": "https://registry.npmmirror.com/qs/download/qs-6.10.1.tgz", "integrity": "sha1-STFIL6jWR6Wqt5nFJx0hM7mB+2o=", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/raw-body": {"version": "2.4.1", "resolved": "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz", "integrity": "sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow=", "dependencies": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body/node_modules/http-errors": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "integrity": "sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body/node_modules/setprototypeof": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}, "node_modules/rc": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/rc/download/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "devOptional": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/read-pkg": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/read-pkg/download/read-pkg-1.1.0.tgz?cache=0&sync_timestamp=1628984695234&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fread-pkg%2Fdownload%2Fread-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "dev": true, "dependencies": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz?cache=0&sync_timestamp=1634147799745&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "dev": true, "dependencies": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/find-up": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz?cache=0&sync_timestamp=1633618631704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "dev": true, "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/read-pkg-up/node_modules/path-exists": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/path-exists/download/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "dev": true, "dependencies": {"pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/recursive-iterator": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/recursive-iterator/download/recursive-iterator-3.3.0.tgz", "integrity": "sha1-TkmM5iJ9jEKy4tSWspa8hmwTRRQ=", "engines": {"node": ">=6"}}, "node_modules/redis": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/redis/download/redis-2.8.0.tgz", "integrity": "sha1-ICKI4/WMSfYHnZevehDhMDrhSwI=", "dependencies": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.6.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/redis-commands": {"version": "1.7.0", "resolved": "https://registry.npmmirror.com/redis-commands/download/redis-commands-1.7.0.tgz", "integrity": "sha1-Fab+otWCgeJ7HNGs+0spPieMOok="}, "node_modules/redis-parser": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/redis-parser/download/redis-parser-2.6.0.tgz", "integrity": "sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs=", "engines": {"node": ">=0.10.0"}}, "node_modules/reduce-component": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/reduce-component/-/reduce-component-1.0.1.tgz", "integrity": "sha512-y0wyCcdQul3hI3xHfIs0vg/jSbboQc/YTOAqaxjFG7At+XSexduuOqBVL9SmOLSwa/ldkbzVzdwuk9s2EKTAZg=="}, "node_modules/reflect-metadata": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/reflect-metadata/download/reflect-metadata-0.1.13.tgz", "integrity": "sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag="}, "node_modules/registry-auth-token": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/registry-auth-token/download/registry-auth-token-4.2.1.tgz", "integrity": "sha1-bXtABkQZGJcszV/tzUHcMix5slA=", "dev": true, "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=6.0.0"}}, "node_modules/registry-url": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/registry-url/download/registry-url-5.1.0.tgz", "integrity": "sha1-6YM0tQ1UNLgRNrROxjjZwgCcUAk=", "dev": true, "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=8"}}, "node_modules/request": {"version": "2.88.2", "resolved": "https://registry.npmmirror.com/request/-/request-2.88.2.tgz", "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/request-promise": {"version": "4.2.6", "resolved": "https://registry.npmmirror.com/request-promise/-/request-promise-4.2.6.tgz", "integrity": "sha512-HCHI3DJJUakkOr8fNoCc73E5nU5bqITjOYFMDrKHYOXWXrgD/SBaC7LjwuPymUprRyuF06UK7hd/lMHkmUXglQ==", "deprecated": "request-promise has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142", "dependencies": {"bluebird": "^3.5.0", "request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request-promise-core": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/request-promise-core/-/request-promise-core-1.1.4.tgz", "integrity": "sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==", "dependencies": {"lodash": "^4.17.19"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request/node_modules/form-data": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/request/node_modules/qs": {"version": "6.5.3", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==", "engines": {"node": ">=0.6"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "node_modules/resolve": {"version": "1.20.0", "resolved": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "integrity": "sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=", "dependencies": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-path": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/resolve-path/download/resolve-path-1.4.0.tgz", "integrity": "sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=", "dependencies": {"http-errors": "~1.6.2", "path-is-absolute": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/resolve-path/node_modules/depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "engines": {"node": ">= 0.6"}}, "node_modules/resolve-path/node_modules/http-errors": {"version": "1.6.3", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/resolve-path/node_modules/inherits": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "node_modules/resolve-path/node_modules/setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="}, "node_modules/responselike": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/responselike/download/responselike-1.0.2.tgz", "integrity": "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=", "dev": true, "dependencies": {"lowercase-keys": "^1.0.0"}}, "node_modules/retry": {"version": "0.10.1", "resolved": "https://registry.npmmirror.com/retry/download/retry-0.10.1.tgz", "integrity": "sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=", "engines": {"node": "*"}}, "node_modules/rfdc": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/rfdc/download/rfdc-1.3.0.tgz", "integrity": "sha1-0LfEQasnINBdxM8m4ByJYx2doIs="}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "node_modules/sax": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}, "node_modules/semver": {"version": "6.3.0", "resolved": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0=", "bin": {"semver": "bin/semver.js"}}, "node_modules/semver-diff": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/semver-diff/download/semver-diff-3.1.1.tgz", "integrity": "sha1-Bfd85Z8yXgDicGr9Z7tQbdscoys=", "dev": true, "dependencies": {"semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "node_modules/shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "engines": {"node": ">=0.10.0"}}, "node_modules/shimmer": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/shimmer/download/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc="}, "node_modules/side-channel": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632948374592&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "integrity": "sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8="}, "node_modules/simple-concat": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/simple-concat/download/simple-concat-1.0.1.tgz", "integrity": "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "optional": true}, "node_modules/simple-get": {"version": "2.8.1", "resolved": "https://registry.npmmirror.com/simple-get/download/simple-get-2.8.1.tgz", "integrity": "sha1-DiLpHUV12HYgYgvJEwjVenf0S10=", "optional": true, "dependencies": {"decompress-response": "^3.3.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "node_modules/snappy": {"version": "6.3.5", "resolved": "https://registry.npmmirror.com/snappy/download/snappy-6.3.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsnappy%2Fdownload%2Fsnappy-6.3.5.tgz", "integrity": "sha1-wUuN6o6bwmh4dbXkkdFd2QDmAjw=", "hasInstallScript": true, "optional": true, "dependencies": {"bindings": "^1.3.1", "nan": "^2.14.1", "prebuild-install": "5.3.0"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.20", "resolved": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.20.tgz", "integrity": "sha1-EhZgifj15ejFaSazd2Mzkt0stsk=", "dev": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/spdx-correct": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.1.tgz", "integrity": "sha1-3s6BrJweZxPl99G28X1Gj6U9iak=", "dev": true, "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz", "integrity": "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=", "dev": true}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.10", "resolved": "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.10.tgz", "integrity": "sha1-DZvszN5wA9bGWNSH3UijLwvzAUs=", "dev": true}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "node_modules/sshpk": {"version": "1.17.0", "resolved": "https://registry.npmmirror.com/sshpk/-/sshpk-1.17.0.tgz", "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/stack-chain": {"version": "1.3.7", "resolved": "https://registry.npmmirror.com/stack-chain/download/stack-chain-1.3.7.tgz", "integrity": "sha1-0ZLJ/06moiyUxN1FkXHj8AzqEoU="}, "node_modules/statuses": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1609654090567&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "engines": {"node": ">= 0.6"}}, "node_modules/stealthy-require": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/stealthy-require/-/stealthy-require-1.1.1.tgz", "integrity": "sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g==", "engines": {"node": ">=0.10.0"}}, "node_modules/streamroller": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/streamroller/download/streamroller-1.0.6.tgz", "integrity": "sha1-gWfYSW7Z8Z8F7ksVjZYRMhuMrNk=", "deprecated": "1.x is no longer supported. Please upgrade to 3.x or higher.", "dependencies": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.14"}, "engines": {"node": ">=6.0"}}, "node_modules/streamroller/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dependencies": {"ms": "^2.1.1"}}, "node_modules/streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA==", "engines": {"node": ">=0.8.0"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string-width": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-1.0.2.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/strip-bom/download/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "dependencies": {"is-utf8": "^0.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-eof": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "devOptional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/superagent": {"version": "3.8.3", "resolved": "https://registry.npmmirror.com/superagent/download/superagent-3.8.3.tgz", "integrity": "sha1-Rg6g29t9WxG8T3jeulZfhqF44Sg=", "deprecated": "Please upgrade to superagent v10.2.2+, see release notes at https://github.com/forwardemail/superagent/releases/tag/v10.2.2 - maintenance is supported by Forward Email @ https://forwardemail.net", "dev": true, "dependencies": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "engines": {"node": ">= 4.0"}}, "node_modules/superagent/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/supertest": {"version": "3.4.2", "resolved": "https://registry.npmmirror.com/supertest/download/supertest-3.4.2.tgz?cache=0&sync_timestamp=1629231412398&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupertest%2Fdownload%2Fsupertest-3.4.2.tgz", "integrity": "sha1-utfeLkPWDSfIyuuKs0pnyKX3Gq0=", "deprecated": "Please upgrade to supertest v7.1.3+, see release notes at https://github.com/forwardemail/supertest/releases/tag/v7.1.3 - maintenance is supported by Forward Email @ https://forwardemail.net", "dev": true, "dependencies": {"methods": "^1.1.2", "superagent": "^3.8.3"}, "engines": {"node": ">=6.0.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/swagger-methods": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/swagger-methods/download/swagger-methods-1.0.8.tgz", "integrity": "sha1-i6837oYdPHL/ey+q1tdMYLM24u0=", "deprecated": "This package is no longer being maintained."}, "node_modules/swagger-parser": {"version": "5.0.6", "resolved": "https://registry.npmmirror.com/swagger-parser/download/swagger-parser-5.0.6.tgz", "integrity": "sha1-37hwJPjJyMa7fBOQ6zz9Bl50RA4=", "dependencies": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "json-schema-ref-parser": "^5.1.3", "ono": "^4.0.6", "openapi-schema-validation": "^0.4.2", "swagger-methods": "^1.0.4", "swagger-schema-official": "2.0.0-bab6bed", "z-schema": "^3.23.0"}}, "node_modules/swagger-parser/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dependencies": {"ms": "^2.1.1"}}, "node_modules/swagger-schema-official": {"version": "2.0.0-bab6bed", "resolved": "https://registry.npmmirror.com/swagger-schema-official/download/swagger-schema-official-2.0.0-bab6bed.tgz", "integrity": "sha1-cAcEaNbSl3ylI3suUZyn0Gouo/0="}, "node_modules/tar-fs": {"version": "1.16.3", "resolved": "https://registry.npmmirror.com/tar-fs/download/tar-fs-1.16.3.tgz", "integrity": "sha1-lmpiiEHaLEAQQGqCFny9Xgxy1Qk=", "optional": true, "dependencies": {"chownr": "^1.0.1", "mkdirp": "^0.5.1", "pump": "^1.0.0", "tar-stream": "^1.1.2"}}, "node_modules/tar-fs/node_modules/pump": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/pump/download/pump-1.0.3.tgz", "integrity": "sha1-Xf6DEcM7v2/BgmH580cCxHwIqVQ=", "optional": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/tar-stream": {"version": "1.6.2", "resolved": "https://registry.npmmirror.com/tar-stream/download/tar-stream-1.6.2.tgz", "integrity": "sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=", "optional": true, "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/tar-stream/node_modules/bl": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/bl/download/bl-1.2.3.tgz", "integrity": "sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=", "optional": true, "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "node_modules/thenify": {"version": "3.3.1", "resolved": "https://registry.npmmirror.com/thenify/download/thenify-3.3.1.tgz", "integrity": "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=", "dependencies": {"any-promise": "^1.0.0"}}, "node_modules/thenify-all": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "node_modules/to-buffer": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/to-buffer/download/to-buffer-1.1.1.tgz", "integrity": "sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=", "optional": true}, "node_modules/to-readable-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/to-readable-stream/download/to-readable-stream-1.0.0.tgz", "integrity": "sha1-zgqgwvPfat+FLvtASng+d8BHV3E=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM=", "engines": {"node": ">=0.6"}}, "node_modules/topo": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/topo/download/topo-3.0.3.tgz", "integrity": "sha1-1aZ/suaTB+vusIQC7Coqb1962Vw=", "deprecated": "This module has moved and is now available at @hapi/topo. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues.", "dependencies": {"hoek": "6.x.x"}}, "node_modules/touch": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/touch/download/touch-3.1.0.tgz", "integrity": "sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=", "dev": true, "dependencies": {"nopt": "~1.0.10"}, "bin": {"nodetouch": "bin/nodetouch.js"}}, "node_modules/tough-cookie": {"version": "2.5.0", "resolved": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "node_modules/traverse": {"version": "0.3.9", "resolved": "https://registry.npmmirror.com/traverse/download/traverse-0.3.9.tgz", "integrity": "sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk=", "engines": {"node": "*"}}, "node_modules/ts-node": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/ts-node/download/ts-node-6.2.0.tgz?cache=0&sync_timestamp=1634967366297&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fts-node%2Fdownload%2Fts-node-6.2.0.tgz", "integrity": "sha1-ZaCuKszjGepP16yNfJ8fkMXaa68=", "dev": true, "dependencies": {"arrify": "^1.0.0", "buffer-from": "^1.1.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.6", "yn": "^2.0.0"}, "bin": {"ts-node": "dist/bin.js"}, "engines": {"node": ">=4.2.0"}}, "node_modules/tslib": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "node_modules/tslint": {"version": "5.20.1", "resolved": "https://registry.npmmirror.com/tslint/download/tslint-5.20.1.tgz?cache=0&sync_timestamp=1618847624538&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslint%2Fdownload%2Ftslint-5.20.1.tgz", "integrity": "sha1-5AHortoBUrxE3QfmFANPP4DGe30=", "dependencies": {"@babel/code-frame": "^7.0.0", "builtin-modules": "^1.1.1", "chalk": "^2.3.0", "commander": "^2.12.1", "diff": "^4.0.1", "glob": "^7.1.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "resolve": "^1.3.2", "semver": "^5.3.0", "tslib": "^1.8.0", "tsutils": "^2.29.0"}, "bin": {"tslint": "bin/tslint"}, "engines": {"node": ">=4.8.0"}, "peerDependencies": {"typescript": ">=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >=3.0.0-dev || >= 3.1.0-dev || >= 3.2.0-dev"}}, "node_modules/tslint/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/tslint/node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/tslint/node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dependencies": {"color-name": "1.1.3"}}, "node_modules/tslint/node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "node_modules/tslint/node_modules/diff": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/diff/download/diff-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdiff%2Fdownload%2Fdiff-4.0.2.tgz", "integrity": "sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=", "engines": {"node": ">=0.3.1"}}, "node_modules/tslint/node_modules/semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "bin": {"semver": "bin/semver"}}, "node_modules/tsscmp": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/tsscmp/download/tsscmp-1.0.6.tgz", "integrity": "sha1-hbmVg6w1iexL/vgltQAKqRHWBes=", "engines": {"node": ">=0.6.x"}}, "node_modules/tsutils": {"version": "2.29.0", "resolved": "https://registry.npmmirror.com/tsutils/download/tsutils-2.29.0.tgz", "integrity": "sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k=", "dependencies": {"tslib": "^1.8.1"}, "peerDependencies": {"typescript": ">=2.1.0 || >=2.1.0-dev || >=2.2.0-dev || >=2.3.0-dev || >=2.4.0-dev || >=2.5.0-dev || >=2.6.0-dev || >=2.7.0-dev || >=2.8.0-dev || >=2.9.0-dev || >= 3.0.0-dev || >= 3.1.0-dev"}}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmmirror.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="}, "node_modules/typedarray-to-buffer": {"version": "3.1.5", "resolved": "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=", "dev": true, "dependencies": {"is-typedarray": "^1.0.0"}}, "node_modules/typescript": {"version": "3.9.10", "resolved": "https://registry.npmmirror.com/typescript/download/typescript-3.9.10.tgz", "integrity": "sha1-cPORCselHta+952ngAaQsZv3eLg=", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/typescript-json-schema": {"version": "0.36.0", "resolved": "https://registry.npmmirror.com/typescript-json-schema/download/typescript-json-schema-0.36.0.tgz?cache=0&sync_timestamp=1631896031454&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftypescript-json-schema%2Fdownload%2Ftypescript-json-schema-0.36.0.tgz", "integrity": "sha1-CHSKOfzCPZKIGaIPOU/kwPm4hjk=", "dependencies": {"glob": "~7.1.2", "json-stable-stringify": "^1.0.1", "typescript": "^3.0.1", "yargs": "^12.0.1"}, "bin": {"typescript-json-schema": "bin/typescript-json-schema"}}, "node_modules/typescript-json-schema/node_modules/ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "engines": {"node": ">=4"}}, "node_modules/typescript-json-schema/node_modules/glob": {"version": "7.1.7", "resolved": "https://registry.npmmirror.com/glob/download/glob-7.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz", "integrity": "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=", "deprecated": "Glob versions prior to v9 are no longer supported", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/typescript-json-schema/node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "engines": {"node": ">=4"}}, "node_modules/typescript-json-schema/node_modules/string-width": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/typescript-json-schema/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/typescript-json-schema/node_modules/yargs": {"version": "12.0.5", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-12.0.5.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-12.0.5.tgz", "integrity": "sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM=", "dependencies": {"cliui": "^4.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^11.1.1"}}, "node_modules/undefsafe": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/undefsafe/download/undefsafe-2.0.5.tgz", "integrity": "sha1-OHM7kye9zSJtuIn7cjpu/RYubiw=", "dev": true}, "node_modules/unique-string": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/unique-string/download/unique-string-2.0.0.tgz", "integrity": "sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=", "dev": true, "dependencies": {"crypto-random-string": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/universalify": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz?cache=0&sync_timestamp=1603180004159&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Funiversalify%2Fdownload%2Funiversalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=", "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "engines": {"node": ">= 0.8"}}, "node_modules/update-notifier": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/update-notifier/download/update-notifier-5.1.0.tgz", "integrity": "sha1-SrDXx/NqIx3XMWz3cpMT8CFNmtk=", "dev": true, "dependencies": {"boxen": "^5.0.0", "chalk": "^4.1.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.4.0", "is-npm": "^5.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.1.0", "pupa": "^2.1.1", "semver": "^7.3.4", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/yeoman/update-notifier?sponsor=1"}}, "node_modules/update-notifier/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/update-notifier/node_modules/semver": {"version": "7.3.5", "resolved": "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "integrity": "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=", "dev": true, "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/update-notifier/node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urijs": {"version": "1.19.7", "resolved": "https://registry.npmmirror.com/urijs/download/urijs-1.19.7.tgz", "integrity": "sha1-T1lOWRE5KP6mPADOaI+zlbEWirk="}, "node_modules/url-parse-lax": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/url-parse-lax/download/url-parse-lax-3.0.0.tgz", "integrity": "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=", "dev": true, "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "node_modules/uuid": {"version": "3.4.0", "resolved": "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "bin": {"uuid": "bin/uuid"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/validator": {"version": "10.11.0", "resolved": "https://registry.npmmirror.com/validator/download/validator-10.11.0.tgz", "integrity": "sha1-ADEI6m6amHTTHMyeUAaFbM12sig=", "engines": {"node": ">= 0.10"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "engines": {"node": ">= 0.8"}}, "node_modules/verror": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/verror/node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="}, "node_modules/which": {"version": "1.3.1", "resolved": "https://registry.npmmirror.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-module": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="}, "node_modules/which-pm-runs": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/which-pm-runs/download/which-pm-runs-1.0.0.tgz", "integrity": "sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=", "optional": true}, "node_modules/wide-align": {"version": "1.1.5", "resolved": "https://registry.npmmirror.com/wide-align/download/wide-align-1.1.5.tgz", "integrity": "sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=", "optional": true, "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/widest-line": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/widest-line/download/widest-line-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwidest-line%2Fdownload%2Fwidest-line-3.1.0.tgz", "integrity": "sha1-gpIzO79my0X/DeFgOxNreuFJbso=", "dev": true, "dependencies": {"string-width": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/widest-line/node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/wrap-ansi": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "node_modules/write-file-atomic": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz", "integrity": "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=", "dev": true, "dependencies": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "node_modules/xdg-basedir": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/xdg-basedir/download/xdg-basedir-4.0.0.tgz", "integrity": "sha1-S8jZmEQDaWIl74OhVzy7y0552xM=", "dev": true, "engines": {"node": ">=8"}}, "node_modules/xml2js": {"version": "0.4.23", "resolved": "https://registry.npmmirror.com/xml2js/-/xml2js-0.4.23.tgz", "integrity": "sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/xmlbuilder": {"version": "11.0.1", "resolved": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==", "engines": {"node": ">=4.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-4.0.3.tgz", "integrity": "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="}, "node_modules/yallist": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="}, "node_modules/yargs": {"version": "7.1.2", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-7.1.2.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-7.1.2.tgz", "integrity": "sha1-Y6Cl1CFDh5/bswNwdBN04GQdVds=", "dev": true, "dependencies": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.1"}}, "node_modules/yargs-parser": {"version": "11.1.1", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-11.1.1.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-11.1.1.tgz", "integrity": "sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ=", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "node_modules/yargs/node_modules/camelcase": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/yargs/node_modules/cliui": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-3.2.0.tgz", "integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "dev": true, "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "node_modules/yargs/node_modules/invert-kv": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/invert-kv/download/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/yargs/node_modules/lcid": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/lcid/download/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "dev": true, "dependencies": {"invert-kv": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/yargs/node_modules/os-locale": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/os-locale/download/os-locale-1.4.0.tgz?cache=0&sync_timestamp=1633618411079&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-1.4.0.tgz", "integrity": "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=", "dev": true, "dependencies": {"lcid": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/yargs/node_modules/which-module": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/which-module/download/which-module-1.0.0.tgz", "integrity": "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8=", "dev": true}, "node_modules/yargs/node_modules/y18n": {"version": "3.2.2", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-3.2.2.tgz", "integrity": "sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=", "dev": true}, "node_modules/yargs/node_modules/yargs-parser": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-5.0.1.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-5.0.1.tgz", "integrity": "sha1-ft4ynB2M274gm9Jc25kOmx67s5Q=", "dev": true, "dependencies": {"camelcase": "^3.0.0", "object.assign": "^4.1.0"}}, "node_modules/ylru": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/ylru/download/ylru-1.2.1.tgz", "integrity": "sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8=", "engines": {"node": ">= 4.0.0"}}, "node_modules/yn": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/yn/download/yn-2.0.0.tgz?cache=0&sync_timestamp=1628974764210&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyn%2Fdownload%2Fyn-2.0.0.tgz", "integrity": "sha1-5a2ryKz0CPY4X8dklWhMiOavaJo=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/z-schema": {"version": "3.25.1", "resolved": "https://registry.npmmirror.com/z-schema/download/z-schema-3.25.1.tgz", "integrity": "sha1-fhRmO+K5YAPZOKVvZE+4VhZD+34=", "dependencies": {"core-js": "^2.5.7", "lodash.get": "^4.0.0", "lodash.isequal": "^4.0.0", "validator": "^10.0.0"}, "bin": {"z-schema": "bin/z-schema"}, "optionalDependencies": {"commander": "^2.7.1"}}}, "dependencies": {"@babel/code-frame": {"version": "7.15.8", "resolved": "https://registry.npmmirror.com/@babel/code-frame/download/@babel/code-frame-7.15.8.tgz", "integrity": "sha1-RZkMR62tsAwDZ3uqiSIffMI9JQM=", "requires": {"@babel/highlight": "^7.14.5"}}, "@babel/helper-validator-identifier": {"version": "7.15.7", "resolved": "https://registry.npmmirror.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.15.7.tgz", "integrity": "sha1-Ig35k7/pBKSmsCq08zhaXr9uI4k="}, "@babel/highlight": {"version": "7.14.5", "resolved": "https://registry.npmmirror.com/@babel/highlight/download/@babel/highlight-7.14.5.tgz", "integrity": "sha1-aGGlLwOWZAUAH2qlNKAaJNmejNk=", "requires": {"@babel/helper-validator-identifier": "^7.14.5", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}}}, "@eagle/common-service-node": {"version": "3.0.3", "resolved": "http://npm.mail.netease.com/registry/@eagle/common-service-node/-/@eagle/common-service-node-3.0.3.tgz", "integrity": "sha1-6+MeQe29jx2AqgVHhmUWhIxuBjg=", "requires": {"@tiger/boot": "^3.0.0", "@tiger/core": "^3.0.0", "@tiger/openid": "^3.0.0", "@tiger/proxy": "^3.0.0", "@tiger/request": "^1.0.5", "@tiger/swagger": "3.0.6", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "debug": "^4.1.0", "request": "^2.88.0", "request-promise": "^4.2.2"}, "dependencies": {"@tiger/boot": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/-/@tiger/boot-3.0.1.tgz", "integrity": "sha1-dmrW5RGzc/Zr9QKif560QPkjEXo=", "requires": {"@tiger/error": "^3.0.0", "cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "@tiger/core": {"version": "3.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/-/@tiger/core-3.0.2.tgz", "integrity": "sha1-ixx53GPOYhWuKgkRLfBNrafPRTE=", "requires": {"@tiger/tslint": "^3.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/error": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/-/@tiger/error-3.0.0.tgz", "integrity": "sha1-mYYuvQse9eptp+ouGp1B35krYZA=", "requires": {"@tiger/logger": "^3.0.0", "@types/boom": "^7.2.1", "@types/koa": "^2.0.46", "boom": "^7.2.2", "koa": "^2.6.1"}}, "@tiger/filter": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/-/@tiger/filter-3.0.0.tgz", "integrity": "sha1-dk5fVgiezmftgBguLdOi7of3g04=", "requires": {"@tiger/core": "^3.0.0", "path-to-regexp": "^1.7.0"}}, "@tiger/logger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/-/@tiger/logger-3.0.6.tgz", "integrity": "sha1-WLV7FfCWQ20ZYUlmg/ajHn7sQ2g=", "requires": {"@tiger/filter": "^3.0.0", "log4js": "^4.0.2"}}, "@tiger/openid": {"version": "3.0.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/-/@tiger/openid-3.0.1.tgz", "integrity": "sha1-RG/whZYW9mrv44B3uWj6GSjY5JM=", "requires": {"@tiger/core": "^3.0.0", "@tiger/filter": "^3.0.0"}}, "@tiger/proxy": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/-/@tiger/proxy-3.0.0.tgz", "integrity": "sha1-o6AC83NebS2YD+NyNgwdouXQCXE=", "requires": {"@tiger/core": "^3.0.0", "@tiger/logger": "^3.0.0", "@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0", "path-to-regexp": "^1.7.0"}}, "@tiger/request": {"version": "1.0.5", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/-/@tiger/request-1.0.5.tgz", "integrity": "sha1-SZLfxqk0qE5tOddJO6xCgEpb5K4=", "requires": {"@tiger/core": "^1.0.1", "@tiger/logger": "^1.0.0", "axios": "^0.18.0"}, "dependencies": {"@tiger/core": {"version": "1.0.25", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/-/@tiger/core-1.0.25.tgz", "integrity": "sha1-0/tkAxu8JCbidJPOt7i5kf3jLnM=", "requires": {"@tiger/tslint": "^1.0.0", "@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "^2.0.46", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/filter": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/-/@tiger/filter-1.0.2.tgz", "integrity": "sha1-N215eL2ACEtIJwpV2oZCl6SsxhU=", "requires": {"@tiger/core": "^1.0.1", "path-to-regexp": "^1.7.0"}}, "@tiger/logger": {"version": "1.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/-/@tiger/logger-1.0.3.tgz", "integrity": "sha1-mUoM6udRh4LdI8iRbmkEwP0GUMY=", "requires": {"@tiger/core": "^1.0.1", "@tiger/filter": "^1.0.0", "log4js": "^3.0.6"}}, "@tiger/tslint": {"version": "1.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/-/@tiger/tslint-1.0.2.tgz", "integrity": "sha1-oXELDrApdS0mUS3g0NsG/22cC4c=", "requires": {"tslint": "^5.12.0"}}, "debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "requires": {"ms": "^2.1.1"}}, "log4js": {"version": "3.0.6", "resolved": "https://registry.npmmirror.com/log4js/-/log4js-3.0.6.tgz", "integrity": "sha512-ezXZk6oPJCWL483zj64pNkMuY/NcRX5MPiB0zE6tjZM137aeusrOnW1ecxgF9cmwMWkBMhjteQxBPoZBh9FDxQ==", "requires": {"circular-json": "^0.5.5", "date-format": "^1.2.0", "debug": "^3.1.0", "rfdc": "^1.1.2", "streamroller": "0.7.0"}}}}, "@tiger/swagger": {"version": "3.0.6", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/-/@tiger/swagger-3.0.6.tgz", "integrity": "sha1-lGoReNAUGtZLPt0VtmG/c4amyMg=", "requires": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}}, "date-format": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/date-format/-/date-format-1.2.0.tgz", "integrity": "sha512-lAJqBmFzCLcDJdI9cEnJ7loSkLTh1PbIgZUndlzvYbf6NyFEr5n9rQhOwr6CIGwZqyQ3sYeQQiP9NOVQmgmRMA=="}, "koa-send": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/koa-send/-/koa-send-5.0.1.tgz", "integrity": "sha512-tmcyQ/wXXuxpDxyNXv5yNNkdAMdFRqwtegBXUaowiQzUKqJehttS0x2j0eOZDQAyloAth5w6wwBImnFzkUz3pQ==", "requires": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}}, "streamroller": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/streamroller/-/streamroller-0.7.0.tgz", "integrity": "sha512-WREzfy0r0zUqp3lGO096wRuUp7ho1X6uo/7DJfTlEi0Iv/4gT7YHqXDjKC2ioVGBZtE8QzsQD9nx1nIuoZ57jQ==", "requires": {"date-format": "^1.2.0", "debug": "^3.1.0", "mkdirp": "^0.5.1", "readable-stream": "^2.3.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "requires": {"ms": "^2.1.1"}}}}}}, "@eagle/umc-selector-node": {"version": "5.0.0", "resolved": "http://npm.mail.netease.com/registry/@eagle/umc-selector-node/-/@eagle/umc-selector-node-5.0.0.tgz", "integrity": "sha1-+nN3Jd/o+AdcyigcPMdtJsaf6JI=", "requires": {"@tiger/filter": "^4.0.65", "@tiger/logger": "^4.0.65", "@tiger/tiger-extract-header": "^0.0.2"}, "dependencies": {"@tiger/tiger-extract-header": {"version": "0.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/-/@tiger/tiger-extract-header-0.0.2.tgz", "integrity": "sha1-W0ENAngdXZNAQqqP5d2q3ob9ft0=", "requires": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}}}, "@eagle/workflow-node": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@eagle/workflow-node/download/@eagle/workflow-node-4.0.2.tgz", "integrity": "sha1-tYsggiTh5LCEHW3CoqqzMTXISlg="}, "@eagler/authorizion-node": {"version": "1.3.4", "resolved": "http://npm.mail.netease.com/registry/@eagler/authorizion-node/download/@eagler/authorizion-node-1.3.4.tgz", "integrity": "sha1-tLo5l6bR+rLygGB9PHAB35fhY4I=", "requires": {"@tiger/tiger-extract-header": "^0.0.3"}, "dependencies": {"@tiger/tiger-extract-header": {"version": "0.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/download/@tiger/tiger-extract-header-0.0.3.tgz", "integrity": "sha1-LRYiryEUQDVAoCCLCpbmsG0YDrs=", "requires": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}}}, "@eagler/bussiness-components-node": {"version": "0.0.45", "resolved": "http://npm.mail.netease.com/registry/@eagler/bussiness-components-node/-/@eagler/bussiness-components-node-0.0.45.tgz", "integrity": "sha1-U+zukOtteygFnMRNGxClcKX+mig=", "requires": {"@eagle/common-service-node": "^3.0.3", "@eagle/workflow-node": "^4.0.0", "@tiger/apolloy": "^4.0.0", "@tiger/boot": "^4.0.0", "@tiger/cache": "^4.0.0", "@tiger/core": "^4.0.0", "@tiger/ejs": "^4.0.0", "@tiger/error": "^4.0.0", "@tiger/filter": "^4.0.0", "@tiger/health": "^4.0.0", "@tiger/info": "^4.0.0", "@tiger/logger": "^4.0.0", "@tiger/microconfig": "0.0.3", "@tiger/monitor": "^4.0.42", "@tiger/openid": "^4.0.0", "@tiger/permission": "^4.0.0", "@tiger/proxy": "^4.0.0", "@tiger/request": "^4.0.0", "@tiger/security": "^4.0.0", "@tiger/session": "^4.0.0", "@tiger/swagger": "^4.0.0", "@tiger/validator": "^4.0.0", "@types/request": "^2.48.1", "@types/request-promise": "^4.1.42", "boom": "^7.1.1", "debug": "^4.1.0", "koa": "^2.5.3", "koa-body": "^4.0.4", "koa-compose": "^4.0.0", "koa-etag": "^3.0.0", "koa-multer": "^1.0.2", "koa-router": "^7.3.0", "koa-send": "^4.1.2", "nos-node-sdk": "0.0.4", "path-to-regexp": "^1.1.1", "request": "^2.88.0", "request-promise": "^4.2.2"}}, "@node-common/collector": {"version": "0.0.12", "resolved": "http://npm.mail.netease.com/registry/@node-common/collector/download/@node-common/collector-0.0.12.tgz", "integrity": "sha1-6lbQjK21K5/5rCkK7eA0mTBEMMc=", "requires": {"debug": "^4.1.1", "kafka-node": "^5.0.0"}, "dependencies": {"kafka-node": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/kafka-node/download/kafka-node-5.0.0.tgz", "integrity": "sha1-S29lzB136+VlhZ37j5V17RXVQ8A=", "requires": {"async": "^2.6.2", "binary": "~0.3.0", "bl": "^2.2.0", "buffer-crc32": "~0.2.5", "buffermaker": "~1.2.0", "debug": "^2.1.3", "denque": "^1.3.0", "lodash": "^4.17.4", "minimatch": "^3.0.2", "nested-error-stacks": "^2.0.0", "optional": "^0.1.3", "retry": "^0.10.1", "snappy": "^6.0.1", "uuid": "^3.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "@sindresorhus/is": {"version": "0.14.0", "resolved": "https://registry.npmmirror.com/@sindresorhus/is/download/@sindresorhus/is-0.14.0.tgz", "integrity": "sha1-n7OjzzEyMoFR81PeRjLgHlIQK+o=", "dev": true}, "@szmarczak/http-timer": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/@szmarczak/http-timer/download/@szmarczak/http-timer-1.1.2.tgz", "integrity": "sha1-sWZeLEYaLNkvTBu/UNVFTeDUtCE=", "dev": true, "requires": {"defer-to-connect": "^1.0.1"}}, "@tiger/apolloy": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/apolloy/download/@tiger/apolloy-4.4.1.tgz", "integrity": "sha1-Z9ZAF6KEHDahaTtB+GnLg6QMBoM=", "requires": {"@tiger/logger": "^4.4.1", "fs-extra": "^7.0.0", "reflect-metadata": "^0.1.12"}}, "@tiger/boot": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/boot/download/@tiger/boot-4.4.1.tgz", "integrity": "sha1-ZGxceTfc1DF1BmTcH1ZBUO96fog=", "requires": {"@node-common/collector": "^0.0.12", "cron": "^1.5.0", "debug": "^4.1.0", "koa": "^2.6.1", "koa-etag": "^3.0.0", "koa-router": "^7.4.0", "reflect-metadata": "^0.1.12"}}, "@tiger/cache": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/cache/download/@tiger/cache-4.4.1.tgz", "integrity": "sha1-T4DgviEO6f5iGLE55c37H1yneNI=", "requires": {"@types/debug": "^4.1.4", "@types/lru-cache": "^4.0.0", "debug": "^4.1.1", "lru-cache": "^4.0.0", "node-object-hash": "^1.4.2", "redis": "^2.8.0"}}, "@tiger/cls-hooked": {"version": "4.3.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/cls-hooked/download/@tiger/cls-hooked-4.3.0.tgz", "integrity": "sha1-bh4kavw2U4vybVps/vuHYWPouog=", "requires": {"async-hook-jl": "^1.7.6", "emitter-listener": "^1.0.1"}}, "@tiger/core": {"version": "4.4.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/core/download/@tiger/core-4.4.0.tgz", "integrity": "sha1-23+GE+n/NQIeYx6KzmF5G8rZ2Jo=", "requires": {"@types/boom": "^7.2.1", "@types/formidable": "^1.0.31", "@types/koa": "2.11.3", "boom": "^7.2.2", "formidable": "^1.2.1", "koa": "^2.6.1", "koa-body": "^4.0.6", "path-to-regexp": "^1.7.0"}}, "@tiger/ejs": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/ejs/download/@tiger/ejs-4.4.1.tgz", "integrity": "sha1-jkSEpKkVuoEr2Hf4B/iMHmmc6VE=", "requires": {"koa-ejs": "^4.1.2", "shimmer": "^1.2.0"}}, "@tiger/error": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/error/download/@tiger/error-4.4.1.tgz", "integrity": "sha1-fkp4Sj98PnLXJ8d+5AfBp1jhX+8=", "requires": {"@types/boom": "^7.2.1", "@types/koa": "2.11.3", "boom": "^7.2.2", "koa": "^2.6.1"}}, "@tiger/filter": {"version": "4.4.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/filter/download/@tiger/filter-4.4.0.tgz", "integrity": "sha1-iKW0k03mZwng9+hC8FUnG6lSroE=", "requires": {"koa": "^2.6.1", "path-to-regexp": "^1.7.0"}}, "@tiger/health": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/health/download/@tiger/health-4.4.1.tgz", "integrity": "sha1-+F7WI1odPPV7xg+9dzJ0VwFG1n8="}, "@tiger/info": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/info/download/@tiger/info-4.4.1.tgz", "integrity": "sha1-BqRsJq0ZubGDmN31Buzs4lvpPhQ="}, "@tiger/logger": {"version": "4.4.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/logger/download/@tiger/logger-4.4.2.tgz", "integrity": "sha1-bHJU8AOvh0046kaPVFxbwhYA5no=", "requires": {"@tiger/trace": "^4.4.1", "log4js": "^4.0.2"}}, "@tiger/microconfig": {"version": "0.0.3", "resolved": "http://npm.mail.netease.com/registry/@tiger/microconfig/download/@tiger/microconfig-0.0.3.tgz", "integrity": "sha1-hVnN5J3QGO1QTCNjEPsUNhfSvXs=", "requires": {"koa": "^2.7.0"}}, "@tiger/monitor": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/monitor/-/@tiger/monitor-4.4.1.tgz", "integrity": "sha1-9z8DjwQv0XUSlW+EoAYWQlurnHw=", "requires": {"axios": "^0.19.0", "class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13", "shimmer": "^1.2.1"}, "dependencies": {"axios": {"version": "0.19.2", "resolved": "https://registry.npmmirror.com/axios/-/axios-0.19.2.tgz", "integrity": "sha512-fjgm5MvRHLhx+osE2xoekY70AhARk3a6hkN+3Io1jc00jtquGvxYlKlsFUhmUET0V5te6CcZI7lcv2Ym61mjHA==", "requires": {"follow-redirects": "1.5.10"}}, "debug": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/debug/-/debug-3.1.0.tgz", "integrity": "sha512-OX8XqP7/1a9cqkxYw2yXss15f26NKWBpDXQd0/uK/KPqdQhxbPa994hnzjcE2VqQpDslf55723cKPUOGSmMY3g==", "requires": {"ms": "2.0.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/follow-redirects/-/follow-redirects-1.5.10.tgz", "integrity": "sha512-0V5l4Cizzvqt5D44aTXbFZz+FtyXV1vrDN6qrelxtfYQKW0KO0W2T/hkE8xvGa/540LkZlkaUjO4ailYTFtHVQ==", "requires": {"debug": "=3.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}}}, "@tiger/openid": {"version": "4.0.2", "resolved": "http://npm.mail.netease.com/registry/@tiger/openid/download/@tiger/openid-4.0.2.tgz", "integrity": "sha1-cyKjzLuOG72Ptpe8zgRoBGshvHM="}, "@tiger/permission": {"version": "4.1.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/permission/download/@tiger/permission-4.1.0.tgz", "integrity": "sha1-ZntWya+cQFhhGTLK0IWNL4NHpAs=", "requires": {"@types/koa": "^2.0.46", "koa": "^2.5.1"}}, "@tiger/proxy": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/proxy/download/@tiger/proxy-4.4.1.tgz", "integrity": "sha1-AZ19YbP+jpl7S1geDVdCtYn9C2o=", "requires": {"@types/http-proxy": "^1.16.2", "http-proxy": "^1.17.0"}}, "@tiger/request": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/request/download/@tiger/request-4.4.1.tgz", "integrity": "sha1-RM+O3Z1ZFj8kMESqXRZUqego3EY=", "requires": {"axios": "^0.18.0"}}, "@tiger/security": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/security/download/@tiger/security-4.4.1.tgz", "integrity": "sha1-ZlsoMxXyvKFUDipPi8SV2kncvtA="}, "@tiger/session": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/session/download/@tiger/session-4.4.1.tgz", "integrity": "sha1-DpluxJeFb4HSTDF5jyoy1UUSvSc=", "requires": {"@types/koa": "2.11.3", "@types/redis": "^2.8.6", "koa": "^2.5.1", "lru-cache": "^4.1.1", "redis": "^2.8.0"}}, "@tiger/swagger": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/swagger/download/@tiger/swagger-4.4.1.tgz", "integrity": "sha1-eFYO6I+OwExvXDaE+/VcW9cSAho=", "requires": {"glob": "^7.1.3", "koa": "^2.5.3", "koa-compose": "^4.1.0", "koa-router": "^7.4.0", "koa-send": "^5.0.0", "recursive-iterator": "^3.3.0", "swagger-parser": "^5.0.6", "typescript-json-schema": "^0.36.0"}, "dependencies": {"koa-send": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/koa-send/download/koa-send-5.0.1.tgz", "integrity": "sha1-Odzuv6+zldDWC+r/ujpwtPVD/nk=", "requires": {"debug": "^4.1.1", "http-errors": "^1.7.3", "resolve-path": "^1.4.0"}}}}, "@tiger/tiger-extract-header": {"version": "0.0.4", "resolved": "http://npm.mail.netease.com/registry/@tiger/tiger-extract-header/download/@tiger/tiger-extract-header-0.0.4.tgz", "integrity": "sha1-7xYL9voxcz6FnWUAcF+BbrNI3Gk=", "requires": {"@tiger/filter": "^4.0.51", "koa": "^2.7.0"}}, "@tiger/trace": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/trace/download/@tiger/trace-4.4.1.tgz", "integrity": "sha1-psHh26UP2N8l+SoTpWx951rXrAg=", "requires": {"@tiger/cls-hooked": "^4.3.0", "kafka-node": "^4.1.3", "semver": "^6.1.2", "shimmer": "^1.2.1"}}, "@tiger/tslint": {"version": "3.0.0", "resolved": "http://npm.mail.netease.com/registry/@tiger/tslint/-/@tiger/tslint-3.0.0.tgz", "integrity": "sha1-kP/yftI8eupyO4LbelJYwq2Bh94="}, "@tiger/validator": {"version": "4.4.1", "resolved": "http://npm.mail.netease.com/registry/@tiger/validator/download/@tiger/validator-4.4.1.tgz", "integrity": "sha1-3w7sN2hY4EiHFYQZu9YhcrEoYRE=", "requires": {"class-transformer": "^0.2.0", "class-validator": "^0.9.1", "joi": "^14.3.1", "reflect-metadata": "^0.1.13"}}, "@types/accepts": {"version": "1.3.5", "resolved": "https://registry.npmmirror.com/@types/accepts/download/@types/accepts-1.3.5.tgz", "integrity": "sha1-w0vsEVz8dG4E/loFnfTOfns5FXU=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/bluebird": {"version": "3.5.36", "resolved": "https://registry.npmmirror.com/@types/bluebird/-/bluebird-3.5.36.tgz", "integrity": "sha512-HBNx4lhkxN7bx6P0++W8E289foSu8kO8GCk2unhuVggO+cE7rh9DhZUyPhUxNRG9m+5B5BTKxZQ5ZP92x/mx9Q=="}, "@types/body-parser": {"version": "1.19.1", "resolved": "https://registry.npmmirror.com/@types/body-parser/download/@types/body-parser-1.19.1.tgz", "integrity": "sha1-DAF0xCp9AXuBgwPUtdlpywt1kpw=", "requires": {"@types/connect": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/boom": {"version": "7.3.1", "resolved": "https://registry.npmmirror.com/@types/boom/download/@types/boom-7.3.1.tgz", "integrity": "sha1-r+w+6e1WrCL13/xFrjOpP8D5pIE="}, "@types/caseless": {"version": "0.12.2", "resolved": "https://registry.npmmirror.com/@types/caseless/-/caseless-0.12.2.tgz", "integrity": "sha512-6ckxMjBBD8URvjB6J3NcnuAn5Pkl7t3TizAg+xdlzzQGSPSmBcXf8KoIH0ua/i+tio+ZRUHEXp0HEmvaR4kt0w=="}, "@types/chai": {"version": "4.2.22", "resolved": "https://registry.npmmirror.com/@types/chai/download/@types/chai-4.2.22.tgz", "integrity": "sha1-RwINfkzxkZTUO1IC8191vSrTXOc=", "dev": true}, "@types/connect": {"version": "3.4.35", "resolved": "https://registry.npmmirror.com/@types/connect/download/@types/connect-3.4.35.tgz", "integrity": "sha1-X89q5EXkAh0fwiGaSHPMc6O7KtE=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmmirror.com/@types/content-disposition/download/@types/content-disposition-0.5.4.tgz", "integrity": "sha1-3kjPAcecnxVgvP2K5DIXqwKGV/g="}, "@types/cookiejar": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/@types/cookiejar/download/@types/cookiejar-2.1.2.tgz", "integrity": "sha1-Zq2TMfY/6KPT2djG45Bt0Q9kRug=", "dev": true}, "@types/cookies": {"version": "0.7.7", "resolved": "https://registry.npmmirror.com/@types/cookies/download/@types/cookies-0.7.7.tgz", "integrity": "sha1-epJFPR0WOJwFpTAe71ZvNJRs/YE=", "requires": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/debug": {"version": "4.1.7", "resolved": "https://registry.npmmirror.com/@types/debug/download/@types/debug-4.1.7.tgz", "integrity": "sha1-fMDqdhUJEkcJuLLRCQ2PbBeq24I=", "requires": {"@types/ms": "*"}}, "@types/ejs": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/@types/ejs/download/@types/ejs-2.7.0.tgz", "integrity": "sha1-vITgg+rjj2Sih6bauQErvh2W4pU=", "dev": true}, "@types/etag": {"version": "1.8.1", "resolved": "https://registry.npmmirror.com/@types/etag/download/@types/etag-1.8.1.tgz", "integrity": "sha1-WTyo3bQ6yz2wSb0JVf1k0oGrWLk=", "dev": true, "requires": {"@types/node": "*"}}, "@types/express": {"version": "4.17.13", "resolved": "https://registry.npmmirror.com/@types/express/download/@types/express-4.17.13.tgz", "integrity": "sha1-p24plXKJmbq1GjP6vOHXBaNwkDQ=", "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.24", "resolved": "https://registry.npmmirror.com/@types/express-serve-static-core/download/@types/express-serve-static-core-4.17.24.tgz", "integrity": "sha1-6kH5O/fg1ZzVp2ZlBo7WqraBXAc=", "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/formidable": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/@types/formidable/download/@types/formidable-1.2.3.tgz", "integrity": "sha1-me5f1Or7qpVEjItnDl6OOJ8q84A=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/http-assert": {"version": "1.5.3", "resolved": "https://registry.npmmirror.com/@types/http-assert/download/@types/http-assert-1.5.3.tgz", "integrity": "sha1-7449Go1Gw4fwSrDy6KuMsMUHhmE="}, "@types/http-proxy": {"version": "1.17.7", "resolved": "https://registry.npmmirror.com/@types/http-proxy/download/@types/http-proxy-1.17.7.tgz", "integrity": "sha1-MOqFzCyGg2g1Kjfw0NNYHiSDTG8=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/joi": {"version": "14.3.4", "resolved": "https://registry.npmmirror.com/@types/joi/download/@types/joi-14.3.4.tgz", "integrity": "sha1-7tHhTLsHcWB5yBQTiDGlIKcloeA=", "dev": true}, "@types/keygrip": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/@types/keygrip/download/@types/keygrip-1.0.2.tgz", "integrity": "sha1-UTq/0lbXrQvx7hhzYGMXszsbKnI="}, "@types/koa": {"version": "2.11.3", "resolved": "https://registry.npmmirror.com/@types/koa/download/@types/koa-2.11.3.tgz", "integrity": "sha1-VA7ON2WBsSvq35pBfdFzG8McFs4=", "requires": {"@types/accepts": "*", "@types/content-disposition": "*", "@types/cookies": "*", "@types/http-assert": "*", "@types/keygrip": "*", "@types/koa-compose": "*", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/koa-compose": {"version": "3.2.5", "resolved": "https://registry.npmmirror.com/@types/koa-compose/download/@types/koa-compose-3.2.5.tgz", "integrity": "sha1-hesugKxQvpXzfM+MQHwJu+NGjp0=", "requires": {"@types/koa": "*"}}, "@types/koa-etag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/@types/koa-etag/download/@types/koa-etag-3.0.0.tgz", "integrity": "sha1-0U09q0XVV3uUvHKWBjHelnUTQdM=", "dev": true, "requires": {"@types/etag": "*", "@types/koa": "*"}}, "@types/koa-router": {"version": "7.0.35", "resolved": "https://registry.npmmirror.com/@types/koa-router/download/@types/koa-router-7.0.35.tgz", "integrity": "sha1-B0QqyV5l7BsEKvT3Kr6kTclrkJ8=", "dev": true, "requires": {"@types/koa": "*"}}, "@types/koa-send": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/@types/koa-send/download/@types/koa-send-4.1.3.tgz", "integrity": "sha1-Fxk8ZHKunl0bma6AhpScxP1pF50=", "dev": true, "requires": {"@types/koa": "*"}}, "@types/lru-cache": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/@types/lru-cache/download/@types/lru-cache-4.1.3.tgz", "integrity": "sha1-7F623YGLegYzbPtzaHIxZLGV+Bg="}, "@types/mime": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/@types/mime/download/@types/mime-1.3.2.tgz", "integrity": "sha1-k+Jb+e51/g/YC1lLxP6w6GIRG1o="}, "@types/ms": {"version": "0.7.31", "resolved": "https://registry.npmmirror.com/@types/ms/download/@types/ms-0.7.31.tgz", "integrity": "sha1-MbfKZAcSij0rvCf+LSGzRTl/YZc="}, "@types/node": {"version": "8.10.66", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-8.10.66.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-8.10.66.tgz", "integrity": "sha1-3QNdQJ3zIqzIPf9ipgLxKleDu7M="}, "@types/qs": {"version": "6.9.7", "resolved": "https://registry.npmmirror.com/@types/qs/download/@types/qs-6.9.7.tgz?cache=0&sync_timestamp=1629708791613&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fqs%2Fdownload%2F%40types%2Fqs-6.9.7.tgz", "integrity": "sha1-Y7t9Bn2xB8weRXwwO8JdUR/r9ss="}, "@types/range-parser": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/@types/range-parser/download/@types/range-parser-1.2.4.tgz?cache=0&sync_timestamp=1629708789398&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Frange-parser%2Fdownload%2F%40types%2Frange-parser-1.2.4.tgz", "integrity": "sha1-zWZ7z90CUhOq+3ylkVqTJZCs3Nw="}, "@types/redis": {"version": "2.8.32", "resolved": "https://registry.npmmirror.com/@types/redis/download/@types/redis-2.8.32.tgz", "integrity": "sha1-HTQwIZr77hD4z6OJ2tJXGgXs+xE=", "requires": {"@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/request": {"version": "2.48.8", "resolved": "https://registry.npmmirror.com/@types/request/-/request-2.48.8.tgz", "integrity": "sha512-whjk1EDJPcAR2kYHRbFl/lKeeKYTi05A15K9bnLInCVroNDCtXce57xKdI0/rQaA3K+6q0eFyUBPmqfSndUZdQ==", "requires": {"@types/caseless": "*", "@types/node": "*", "@types/tough-cookie": "*", "form-data": "^2.5.0"}}, "@types/request-promise": {"version": "4.1.48", "resolved": "https://registry.npmmirror.com/@types/request-promise/-/request-promise-4.1.48.tgz", "integrity": "sha512-sLsfxfwP5G3E3U64QXxKwA6ctsxZ7uKyl4I28pMj3JvV+ztWECRns73GL71KMOOJME5u1A5Vs5dkBqyiR1Zcnw==", "requires": {"@types/bluebird": "*", "@types/request": "*"}}, "@types/serve-static": {"version": "1.13.10", "resolved": "https://registry.npmmirror.com/@types/serve-static/download/@types/serve-static-1.13.10.tgz", "integrity": "sha1-9eDOh5fS18xevtpIpSyWxPpHqNk=", "requires": {"@types/mime": "^1", "@types/node": "*"}, "dependencies": {"@types/node": {"version": "16.11.6", "resolved": "https://registry.npmmirror.com/@types/node/download/@types/node-16.11.6.tgz?cache=0&sync_timestamp=1635213425908&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-16.11.6.tgz", "integrity": "sha1-a+96KgrWhM9ukPz+Mc7KvZzgo64="}}}, "@types/superagent": {"version": "4.1.13", "resolved": "https://registry.npmmirror.com/@types/superagent/download/@types/superagent-4.1.13.tgz", "integrity": "sha1-Cqo/T/lAS5STLR3N+389OdI5l6A=", "dev": true, "requires": {"@types/cookiejar": "*", "@types/node": "*"}}, "@types/supertest": {"version": "2.0.11", "resolved": "https://registry.npmmirror.com/@types/supertest/download/@types/supertest-2.0.11.tgz", "integrity": "sha1-LnD2nyILx3tPZg1ywuGkIx9Ep30=", "dev": true, "requires": {"@types/superagent": "*"}}, "@types/tough-cookie": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/@types/tough-cookie/-/tough-cookie-4.0.2.tgz", "integrity": "sha512-Q5vtl1W5ue16D+nIaW8JWebSSraJVlK+EthKn7e7UcD4KWsaSJ8BqGPXNaPghgtcn/fhvrN17Tv8ksUsQpiplw=="}, "@vscode-snippets/tiger": {"version": "1.0.0", "resolved": "http://npm.mail.netease.com/registry/@vscode-snippets/tiger/download/@vscode-snippets/tiger-1.0.0.tgz", "integrity": "sha1-5y/rBjmBDcPYMYuzQgqbRM77VjY=", "dev": true}, "abbrev": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/abbrev/download/abbrev-1.1.1.tgz", "integrity": "sha1-+PLIh60Qv2f2NPAFtph/7TF5qsg=", "dev": true}, "accepts": {"version": "1.3.7", "resolved": "https://registry.npmmirror.com/accepts/download/accepts-1.3.7.tgz", "integrity": "sha1-UxvHJlF6OytB+FACHGzBXqq1B80=", "requires": {"mime-types": "~2.1.24", "negotiator": "0.6.2"}}, "ajv": {"version": "6.12.6", "resolved": "https://registry.npmmirror.com/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-align": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/ansi-align/download/ansi-align-3.0.1.tgz?cache=0&sync_timestamp=1632743770230&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-align%2Fdownload%2Fansi-align-3.0.1.tgz", "integrity": "sha1-DN8S4RGs53OobpofrRIlxDyxmlk=", "dev": true, "requires": {"string-width": "^4.1.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}}}, "ansi-regex": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-2.1.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8="}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-4.3.0.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "any-promise": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/any-promise/download/any-promise-1.3.0.tgz", "integrity": "sha1-q8av7tzqUugJzcA3au0845Y10X8="}, "anymatch": {"version": "3.1.2", "resolved": "https://registry.npmmirror.com/anymatch/download/anymatch-3.1.2.tgz", "integrity": "sha1-wFV8CWrzLxBhmPT04qODU343hxY=", "dev": true, "requires": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}}, "append-field": {"version": "0.1.0", "resolved": "https://registry.npmmirror.com/append-field/-/append-field-0.1.0.tgz", "integrity": "sha512-8BgHoIwbQZaAQgDZLBu2vQoXHgUpSx4vQK1qv7e6R8YfbiSf4fCaBPJRtM1BaxVn1rIHc5ftv0cklsJ78BkouQ=="}, "aproba": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/aproba/download/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "optional": true}, "are-we-there-yet": {"version": "1.1.7", "resolved": "https://registry.npmmirror.com/are-we-there-yet/download/are-we-there-yet-1.1.7.tgz", "integrity": "sha1-sVR0qTKtq0/4pQ2a36fk6SbyEUY=", "optional": true, "requires": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "argparse": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "requires": {"sprintf-js": "~1.0.2"}}, "arrify": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/arrify/download/arrify-1.0.1.tgz?cache=0&sync_timestamp=1619599497996&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Farrify%2Fdownload%2Farrify-1.0.1.tgz", "integrity": "sha1-iYUI2iIm84DfkEcoRWhJwVAaSw0=", "dev": true}, "asn1": {"version": "0.2.6", "resolved": "https://registry.npmmirror.com/asn1/-/asn1-0.2.6.tgz", "integrity": "sha512-ix/FxPn0MDjeyJ7i/yoHGFt/EX6LyNbxSEhPPXODPL+KB0VPk86UYfL0lMdy+KCnv+fmvIzySwaK5COwqVbWTQ==", "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha512-NfJ4UzBCcQGLDlQq7nHxH+tv3kyZ0hHQqF5BO6J7tNJeP5do1llPr8dZ8zHonfhAu0PHAdMkSo+8o0wxg9lZWw=="}, "assertion-error": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/assertion-error/download/assertion-error-1.1.0.tgz?cache=0&sync_timestamp=1633447542062&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fassertion-error%2Fdownload%2Fassertion-error-1.1.0.tgz", "integrity": "sha1-5gtrDo8wG9l+U3UhW9pAbIURjAs=", "dev": true}, "async": {"version": "2.6.3", "resolved": "https://registry.npmmirror.com/async/download/async-2.6.3.tgz", "integrity": "sha1-1yYl4jRKNlbjo61Pp0n6gymdgv8=", "requires": {"lodash": "^4.17.14"}}, "async-hook-jl": {"version": "1.7.6", "resolved": "https://registry.npmmirror.com/async-hook-jl/download/async-hook-jl-1.7.6.tgz", "integrity": "sha1-T9JcL4ZNuvJ5xhDXO/l7GyhZXmg=", "requires": {"stack-chain": "^1.3.7"}}, "asynckit": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k="}, "aws-sign2": {"version": "0.7.0", "resolved": "https://registry.npmmirror.com/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha512-08kcGqnYf/YmjoRhfxyu+CLxBjUtHLXLXX/vUfx9l2LYzG3c1m61nrpyFUZI6zeS+Li/wWMMidD9KgrqtGq3mA=="}, "aws4": {"version": "1.11.0", "resolved": "https://registry.npmmirror.com/aws4/-/aws4-1.11.0.tgz", "integrity": "sha512-xh1Rl34h6Fi1DC2WWKfxUTVqRsNnr6LsKz2+hfwDxQJWmrx8+c7ylaqBMcHfl1U1r2dsifOvKX3LQuLNZ+XSvA=="}, "axios": {"version": "0.18.1", "resolved": "https://registry.npmmirror.com/axios/download/axios-0.18.1.tgz", "integrity": "sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=", "requires": {"follow-redirects": "1.5.10", "is-buffer": "^2.0.2"}, "dependencies": {"debug": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.1.0.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.1.0.tgz", "integrity": "sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=", "requires": {"ms": "2.0.0"}}, "follow-redirects": {"version": "1.5.10", "resolved": "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.5.10.tgz?cache=0&sync_timestamp=1631622129411&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.5.10.tgz", "integrity": "sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=", "requires": {"debug": "=3.1.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/balanced-match/download/balanced-match-1.0.2.tgz?cache=0&sync_timestamp=1617714233441&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbalanced-match%2Fdownload%2Fbalanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha512-qeFIXtP4MSoi6NLqO12WfqARWWuCKi2Rn/9hJLEmtB5yTNr9DqFWkJRCf2qShWzPeAMRnOgCrq0sg/KLv5ES9w==", "requires": {"tweetnacl": "^0.14.3"}}, "binary": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/binary/download/binary-0.3.0.tgz", "integrity": "sha1-n2BVO8XOjDOG87VTz/R0Yq3sqnk=", "requires": {"buffers": "~0.1.1", "chainsaw": "~0.1.0"}}, "binary-extensions": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/binary-extensions/download/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true}, "bindings": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/bindings/download/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "optional": true, "requires": {"file-uri-to-path": "1.0.0"}}, "bl": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/bl/download/bl-2.2.1.tgz", "integrity": "sha1-jBGntzBlXF1WiYzchxIk9A/ZAdU=", "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "bluebird": {"version": "3.7.2", "resolved": "https://registry.npmmirror.com/bluebird/-/bluebird-3.7.2.tgz", "integrity": "sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg=="}, "boom": {"version": "7.3.0", "resolved": "https://registry.npmmirror.com/boom/download/boom-7.3.0.tgz", "integrity": "sha1-czptlW0zsLGZnaP+bBKZaVDQF7k=", "requires": {"hoek": "6.x.x"}}, "boxen": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/boxen/download/boxen-5.1.2.tgz?cache=0&sync_timestamp=1634028552381&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fboxen%2Fdownload%2Fboxen-5.1.2.tgz", "integrity": "sha1-eIy2hvyDwfSG36ikDGj8K4MdK1A=", "dev": true, "requires": {"ansi-align": "^3.0.0", "camelcase": "^6.2.0", "chalk": "^4.1.0", "cli-boxes": "^2.2.1", "string-width": "^4.2.2", "type-fest": "^0.20.2", "widest-line": "^3.1.0", "wrap-ansi": "^7.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "camelcase": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-6.2.0.tgz", "integrity": "sha1-kkr4gcnVJaydh/QNlk5c6pgqGAk=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmmirror.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/braces/download/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "buffer-alloc": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/buffer-alloc/download/buffer-alloc-1.2.0.tgz", "integrity": "sha1-iQ3ZDZI6hz4I4Q5f1RpX5bfM4Ow=", "optional": true, "requires": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "buffer-alloc-unsafe": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/buffer-alloc-unsafe/download/buffer-alloc-unsafe-1.1.0.tgz", "integrity": "sha1-vX3CauKXLQ7aJTvgYdupkjScGfA=", "optional": true}, "buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmmirror.com/buffer-crc32/download/buffer-crc32-0.2.13.tgz", "integrity": "sha1-DTM+PwDqxQqhRUq9MO+MKl2ackI="}, "buffer-fill": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/buffer-fill/download/buffer-fill-1.0.0.tgz", "integrity": "sha1-+PeLdniYiO858gXNY39o5wISKyw=", "optional": true}, "buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/buffer-from/download/buffer-from-1.1.2.tgz?cache=0&sync_timestamp=1627578450949&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fbuffer-from%2Fdownload%2Fbuffer-from-1.1.2.tgz", "integrity": "sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U="}, "buffermaker": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/buffermaker/download/buffermaker-1.2.1.tgz", "integrity": "sha1-BjH5K4kahLdQ8QNkkayFfHNEKfQ=", "requires": {"long": "1.1.2"}}, "buffers": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/buffers/download/buffers-0.1.1.tgz", "integrity": "sha1-skV5w77U1tOWru5tmorn9Ugqt7s="}, "builtin-modules": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/builtin-modules/download/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="}, "busboy": {"version": "0.2.14", "resolved": "https://registry.npmmirror.com/busboy/-/busboy-0.2.14.tgz", "integrity": "sha512-InWFDomvlkEj+xWLBfU3AvnbVYqeTWmQopiW0tWWEy5yehYm2YkGEc59sUmw/4ty5Zj/b0WHGs1LgecuBSBGrg==", "requires": {"dicer": "0.2.5", "readable-stream": "1.1.x"}, "dependencies": {"readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}}}, "bytes": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/bytes/download/bytes-3.1.0.tgz", "integrity": "sha1-9s95M6Ng4FiPqf3oVlHNx/gF0fY="}, "cache-content-type": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/cache-content-type/download/cache-content-type-1.0.1.tgz", "integrity": "sha1-A1zeKwjuISn0qDFeqPAKANuhRTw=", "requires": {"mime-types": "^2.1.18", "ylru": "^1.2.0"}}, "cacheable-request": {"version": "6.1.0", "resolved": "https://registry.npmmirror.com/cacheable-request/download/cacheable-request-6.1.0.tgz", "integrity": "sha1-IP+4vRYrpL4R6VZ9gj22UQUsqRI=", "dev": true, "requires": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "dependencies": {"get-stream": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/get-stream/download/get-stream-5.2.0.tgz", "integrity": "sha1-SWaheV7lrOZecGxLe+txJX1uItM=", "dev": true, "requires": {"pump": "^3.0.0"}}, "lowercase-keys": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz?cache=0&sync_timestamp=1634551808987&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-2.0.0.tgz", "integrity": "sha1-JgPni3tLAAbLyi+8yKMgJVislHk=", "dev": true}, "pump": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/call-bind/download/call-bind-1.0.2.tgz?cache=0&sync_timestamp=1610403020286&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcall-bind%2Fdownload%2Fcall-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "call-me-maybe": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/call-me-maybe/download/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms="}, "camelcase": {"version": "5.3.1", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA="}, "caseless": {"version": "0.12.0", "resolved": "https://registry.npmmirror.com/caseless/-/caseless-0.12.0.tgz", "integrity": "sha512-4tYFyifaFfGacoiObjJegolkwSU4xQNGbVgUiNYVUxbQ2x2lUsFvY4hVgVzGiIe6WLOPqycWXA40l+PWsxthUw=="}, "chai": {"version": "4.3.4", "resolved": "https://registry.npmmirror.com/chai/download/chai-4.3.4.tgz?cache=0&sync_timestamp=1624607982671&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchai%2Fdownload%2Fchai-4.3.4.tgz", "integrity": "sha1-tV5lWzHh6scJm+TAjCGWT84ubEk=", "dev": true, "requires": {"assertion-error": "^1.1.0", "check-error": "^1.0.2", "deep-eql": "^3.0.1", "get-func-name": "^2.0.0", "pathval": "^1.1.1", "type-detect": "^4.0.5"}}, "chainsaw": {"version": "0.1.0", "resolved": "https://registry.npmmirror.com/chainsaw/download/chainsaw-0.1.0.tgz", "integrity": "sha1-XqtQsor+WAdNDVgpE4iCi15fvJg=", "requires": {"traverse": ">=0.3.0 <0.4"}}, "chalk": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-4.1.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "dependencies": {"has-flag": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/has-flag/download/has-flag-4.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-flag%2Fdownload%2Fhas-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true}, "supports-color": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "requires": {"has-flag": "^4.0.0"}}}}, "check-error": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/check-error/download/check-error-1.0.2.tgz", "integrity": "sha1-V00xLt2Iu13YkS6Sht1sCu1KrII=", "dev": true}, "chokidar": {"version": "3.5.2", "resolved": "https://registry.npmmirror.com/chokidar/download/chokidar-3.5.2.tgz", "integrity": "sha1-26OXb8rbAW9m/TZQIdkWANAcHnU=", "dev": true, "requires": {"anymatch": "~3.1.2", "braces": "~3.0.2", "fsevents": "~2.3.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}}, "chownr": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/chownr/download/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "optional": true}, "ci-info": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ci-info/download/ci-info-2.0.0.tgz", "integrity": "sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=", "dev": true}, "circular-json": {"version": "0.5.9", "resolved": "https://registry.npmmirror.com/circular-json/-/circular-json-0.5.9.tgz", "integrity": "sha512-4ivwqHpIFJZBuhN3g/pEcdbnGUywkBblloGbkglyloVjjR3uT6tieI89MVOfbP2tHX5sgb01FuLgAOzebNlJNQ=="}, "class-transformer": {"version": "0.2.3", "resolved": "https://registry.npmmirror.com/class-transformer/download/class-transformer-0.2.3.tgz", "integrity": "sha1-WYySynHcynP5HMuHXXSjhHzPoy0="}, "class-validator": {"version": "0.9.1", "resolved": "https://registry.npmmirror.com/class-validator/download/class-validator-0.9.1.tgz", "integrity": "sha1-1g5YxdFKvKCkG844z3kq1MRtFTE=", "requires": {"google-libphonenumber": "^3.1.6", "validator": "10.4.0"}, "dependencies": {"validator": {"version": "10.4.0", "resolved": "https://registry.npmmirror.com/validator/download/validator-10.4.0.tgz", "integrity": "sha1-7pmkSvs7te01ChWfBWynKiBM/Dw="}}}, "cli-boxes": {"version": "2.2.1", "resolved": "https://registry.npmmirror.com/cli-boxes/download/cli-boxes-2.2.1.tgz", "integrity": "sha1-3dUDXSUJT84iDpyrQKRYQKRAMY8=", "dev": true}, "cliui": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-4.1.0.tgz", "integrity": "sha1-NIQi2+gtgAswIu709qwQvy5NG0k=", "requires": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}}}, "clone-response": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/clone-response/download/clone-response-1.0.2.tgz", "integrity": "sha1-0dyXOSAxTfZ/vrlCI7TuNQI56Ws=", "dev": true, "requires": {"mimic-response": "^1.0.0"}}, "co": {"version": "4.6.0", "resolved": "https://registry.npmmirror.com/co/download/co-4.6.0.tgz", "integrity": "sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ="}, "co-body": {"version": "5.2.0", "resolved": "https://registry.npmmirror.com/co-body/download/co-body-5.2.0.tgz", "integrity": "sha1-WgpljEYCkTHg46MG9nZHMC9xwSQ=", "requires": {"inflation": "^2.0.0", "qs": "^6.4.0", "raw-body": "^2.2.0", "type-is": "^1.6.14"}}, "code-point-at": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/code-point-at/download/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c="}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true}, "combined-stream": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/combined-stream/download/combined-stream-1.0.8.tgz", "integrity": "sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=", "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3", "resolved": "https://registry.npmmirror.com/commander/download/commander-2.20.3.tgz?cache=0&sync_timestamp=1634886357672&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcommander%2Fdownload%2Fcommander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM="}, "component-emitter": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/component-emitter/download/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "concat-stream": {"version": "1.6.2", "resolved": "https://registry.npmmirror.com/concat-stream/-/concat-stream-1.6.2.tgz", "integrity": "sha512-27HBghJxjiZtIk3Ycvn/4kbJk/1uZuJFfuPEns6LaEvpvG1f0hTea8lilrouyo9mVc2GWdcEZ8OLoGmSADlrCw==", "requires": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "configstore": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/configstore/download/configstore-5.0.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fconfigstore%2Fdownload%2Fconfigstore-5.0.1.tgz", "integrity": "sha1-02UCG130uYzdGH1qOw4/anzF7ZY=", "dev": true, "requires": {"dot-prop": "^5.2.0", "graceful-fs": "^4.1.2", "make-dir": "^3.0.0", "unique-string": "^2.0.0", "write-file-atomic": "^3.0.0", "xdg-basedir": "^4.0.0"}}, "console-control-strings": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/console-control-strings/download/console-control-strings-1.1.0.tgz", "integrity": "sha1-PXz0Rk22RG6mRL9LOVB/mFEAjo4=", "optional": true}, "content-disposition": {"version": "0.5.3", "resolved": "https://registry.npmmirror.com/content-disposition/download/content-disposition-0.5.3.tgz", "integrity": "sha1-4TDK9+cnkIfFYWwgB9BIVpiYT70=", "requires": {"safe-buffer": "5.1.2"}}, "content-type": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/content-type/download/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js="}, "cookiejar": {"version": "2.1.3", "resolved": "https://registry.npmmirror.com/cookiejar/download/cookiejar-2.1.3.tgz?cache=0&sync_timestamp=1632881937184&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcookiejar%2Fdownload%2Fcookiejar-2.1.3.tgz", "integrity": "sha1-/HpiFuQI50QUuQIwBQhC2s2nWsw=", "dev": true}, "cookies": {"version": "0.8.0", "resolved": "https://registry.npmmirror.com/cookies/download/cookies-0.8.0.tgz", "integrity": "sha1-EpPOSzkXQKhAbjyYcOgoxLVPP5A=", "requires": {"depd": "~2.0.0", "keygrip": "~1.1.0"}}, "core-js": {"version": "2.6.12", "resolved": "https://registry.npmmirror.com/core-js/download/core-js-2.6.12.tgz?cache=0&sync_timestamp=1635142905717&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fcore-js%2Fdownload%2Fcore-js-2.6.12.tgz", "integrity": "sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw="}, "core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/core-util-is/download/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="}, "cron": {"version": "1.8.2", "resolved": "https://registry.npmmirror.com/cron/download/cron-1.8.2.tgz", "integrity": "sha1-SsXjxVuowWPYTzQHvelGMtqDcM4=", "requires": {"moment-timezone": "^0.5.x"}}, "cross-spawn": {"version": "6.0.5", "resolved": "https://registry.npmmirror.com/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "requires": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="}}}, "crypto-random-string": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/crypto-random-string/download/crypto-random-string-2.0.0.tgz", "integrity": "sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=", "dev": true}, "dashdash": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha512-jRFi8UDGo6j+odZiEpjazZaWqEal3w/basFjQHQEwVtZJGDpxbH1MeYluwCS8Xq5wmLJooDlMgvVarmWfGM44g==", "requires": {"assert-plus": "^1.0.0"}}, "date-format": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/date-format/download/date-format-2.1.0.tgz", "integrity": "sha1-MdW16iEc9f12TNOLr50DPffhJc8="}, "debug": {"version": "4.3.2", "resolved": "https://registry.npmmirror.com/debug/download/debug-4.3.2.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-4.3.2.tgz", "integrity": "sha1-8KScGKyHeeMdSgxgKd+3aHPHQos=", "requires": {"ms": "2.1.2"}}, "decamelize": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/decamelize/download/decamelize-1.2.0.tgz?cache=0&sync_timestamp=1633055760479&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdecamelize%2Fdownload%2Fdecamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA="}, "decompress-response": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/decompress-response/download/decompress-response-3.3.0.tgz", "integrity": "sha1-gKTdMjdIOEv6JICDYirt7Jgq3/M=", "devOptional": true, "requires": {"mimic-response": "^1.0.0"}}, "deep-eql": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/deep-eql/download/deep-eql-3.0.1.tgz", "integrity": "sha1-38lARACtHI/gI+faHfHBR8S0RN8=", "dev": true, "requires": {"type-detect": "^4.0.0"}}, "deep-equal": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/deep-equal/download/deep-equal-1.0.1.tgz?cache=0&sync_timestamp=1606860754950&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdeep-equal%2Fdownload%2Fdeep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU="}, "deep-extend": {"version": "0.6.0", "resolved": "https://registry.npmmirror.com/deep-extend/download/deep-extend-0.6.0.tgz", "integrity": "sha1-xPp8lUBKF6nD6Mp+FTcxK3NjMKw=", "devOptional": true}, "defer-to-connect": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/defer-to-connect/download/defer-to-connect-1.1.3.tgz", "integrity": "sha1-MxrgUMCNz3ifjIOnuB8O2U9KxZE=", "dev": true}, "define-properties": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "requires": {"object-keys": "^1.0.12"}}, "delayed-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk="}, "delegates": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/delegates/download/delegates-1.0.0.tgz", "integrity": "sha1-hMbhWbgZBP3KWaDvRM2HDTElD5o="}, "denque": {"version": "1.5.1", "resolved": "https://registry.npmmirror.com/denque/download/denque-1.5.1.tgz", "integrity": "sha1-B/Zw4pyaePj67LJWah4sEZKcXL8="}, "depd": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/depd/download/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8="}, "destroy": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/destroy/download/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA="}, "detect-libc": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/detect-libc/download/detect-libc-1.0.3.tgz", "integrity": "sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=", "optional": true}, "dicer": {"version": "0.2.5", "resolved": "https://registry.npmmirror.com/dicer/-/dicer-0.2.5.tgz", "integrity": "sha512-FDvbtnq7dzlPz0wyYlOExifDEZcu8h+rErEXgfxqmLfRfC/kJidEFh4+effJRO3P0xmfqyPbSMG0LveNRfTKVg==", "requires": {"readable-stream": "1.1.x", "streamsearch": "0.1.2"}, "dependencies": {"readable-stream": {"version": "1.1.14", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha512-+MeVjFf4L44XUkhM1eYbD8fyEsxcV81pqMSR5gblfcLCHfZvbrqy4/qYHE+/R5HoBUT11WV5O08Cr1n3YXkWVQ==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}}}, "diff": {"version": "3.5.0", "resolved": "https://registry.npmmirror.com/diff/download/diff-3.5.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdiff%2Fdownload%2Fdiff-3.5.0.tgz", "integrity": "sha1-gAwN0eCov7yVg1wgKtIg/jF+WhI=", "dev": true}, "dot-prop": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/dot-prop/download/dot-prop-5.3.0.tgz", "integrity": "sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=", "dev": true, "requires": {"is-obj": "^2.0.0"}}, "double-ended-queue": {"version": "2.1.0-0", "resolved": "https://registry.npmmirror.com/double-ended-queue/download/double-ended-queue-2.1.0-0.tgz", "integrity": "sha1-ED01J/0xUo9AGIEwyEHv3XgmTlw="}, "duplexer3": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/duplexer3/download/duplexer3-0.1.4.tgz", "integrity": "sha1-7gHdHKwO08vH/b6jfcCo8c4ALOI=", "dev": true}, "ecc-jsbn": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha512-eh9O+hwRHNbG4BLTjEl3nw044CkGm5X6LoaCf7LPp7UU8Qrt47JYNi6nPX8xjW97TKGKm1ouctg0QSpZe9qrnw==", "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "ejs": {"version": "2.7.4", "resolved": "https://registry.npmmirror.com/ejs/download/ejs-2.7.4.tgz", "integrity": "sha1-SGYSh1c9zFPjZsehrlLDoSDuybo="}, "emitter-listener": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/emitter-listener/download/emitter-listener-1.1.2.tgz", "integrity": "sha1-VrFA6PaZI3Wz18ssqxzHQy2WMug=", "requires": {"shimmer": "^1.2.0"}}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmmirror.com/emoji-regex/download/emoji-regex-8.0.0.tgz?cache=0&sync_timestamp=1632751333727&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Femoji-regex%2Fdownload%2Femoji-regex-8.0.0.tgz", "integrity": "sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/encodeurl/download/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "encoding": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/encoding/-/encoding-0.1.13.tgz", "integrity": "sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==", "requires": {"iconv-lite": "^0.6.2"}, "dependencies": {"iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmmirror.com/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "requires": {"safer-buffer": ">= 2.1.2 < 3.0.0"}}}}, "end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmmirror.com/end-of-stream/download/end-of-stream-1.4.4.tgz", "integrity": "sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=", "requires": {"once": "^1.4.0"}}, "error-ex": {"version": "1.3.2", "resolved": "https://registry.npmmirror.com/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "requires": {"is-arrayish": "^0.2.1"}}, "escalade": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/escalade/download/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true}, "escape-goat": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/escape-goat/download/escape-goat-2.1.1.tgz", "integrity": "sha1-Gy3HcANnbEV+x2Cy3GjttkgYhnU=", "dev": true}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "esprima": {"version": "4.0.1", "resolved": "https://registry.npmmirror.com/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE="}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmmirror.com/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmmirror.com/eventemitter3/download/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8="}, "execa": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "requires": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}}, "expand-template": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/expand-template/download/expand-template-2.0.3.tgz", "integrity": "sha1-bhSz/O4POmNA7LV9LokYaSBSpHw=", "optional": true}, "extend": {"version": "3.0.2", "resolved": "https://registry.npmmirror.com/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo="}, "extsprintf": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha512-11Ndz7Nv+mvAC1j0ktTa7fAb0vLyGGX+rMHNBYQviQDGU0Hw7lhctJANqbPhu9nV9/izT/IntTgZ7Im/9LJs9g=="}, "fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmmirror.com/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "file-uri-to-path": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "optional": true}, "fill-range": {"version": "7.0.1", "resolved": "https://registry.npmmirror.com/fill-range/download/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "find-up": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/find-up/download/find-up-3.0.0.tgz?cache=0&sync_timestamp=1633618631704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "requires": {"locate-path": "^3.0.0"}}, "flatted": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/flatted/download/flatted-2.0.2.tgz?cache=0&sync_timestamp=1627541315228&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fflatted%2Fdownload%2Fflatted-2.0.2.tgz", "integrity": "sha1-RXWyHivO50NKqb5mL0t7X5wrUTg="}, "follow-redirects": {"version": "1.14.4", "resolved": "https://registry.npmmirror.com/follow-redirects/download/follow-redirects-1.14.4.tgz?cache=0&sync_timestamp=1631622129411&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffollow-redirects%2Fdownload%2Ffollow-redirects-1.14.4.tgz", "integrity": "sha1-g4/fSKi73XnlLuUfsclOPtmLk3k="}, "forever-agent": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha512-j0K<PERSON><PERSON>hm6zeac4lz3oJ3o65qvgQCcPubiyotZrXqEaG4hNagNYO8qdlUrX5vwqv9ohqeT/Z3j6+yW067yWWdUw=="}, "form-data": {"version": "2.5.1", "resolved": "https://registry.npmmirror.com/form-data/download/form-data-2.5.1.tgz", "integrity": "sha1-8svsV7XlniNxbhKP5E1OXdI4lfQ=", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "format-util": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/format-util/download/format-util-1.0.5.tgz", "integrity": "sha1-H/tFDIoD57zP/kBkMYCRjMKX0nE="}, "formidable": {"version": "1.2.2", "resolved": "https://registry.npmmirror.com/formidable/download/formidable-1.2.2.tgz", "integrity": "sha1-v2muopcpgmdfAIZTQrmCmG9rjdk="}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmmirror.com/fresh/download/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs-constants": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/fs-constants/download/fs-constants-1.0.0.tgz", "integrity": "sha1-a+Dem+mYzhavivwkSXue6bfM2a0=", "optional": true}, "fs-extra": {"version": "7.0.1", "resolved": "https://registry.npmmirror.com/fs-extra/download/fs-extra-7.0.1.tgz", "integrity": "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=", "requires": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "fsevents": {"version": "2.3.2", "resolved": "https://registry.npmmirror.com/fsevents/download/fsevents-2.3.2.tgz", "integrity": "sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=", "dev": true, "optional": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0="}, "gauge": {"version": "2.7.4", "resolved": "https://registry.npmmirror.com/gauge/download/gauge-2.7.4.tgz?cache=0&sync_timestamp=1627307653902&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fgauge%2Fdownload%2Fgauge-2.7.4.tgz", "integrity": "sha1-LANAXHU4w51+s3sxcCLjJfsBi/c=", "optional": true, "requires": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "get-caller-file": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-1.0.3.tgz", "integrity": "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o="}, "get-func-name": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/get-func-name/download/get-func-name-2.0.0.tgz?cache=0&sync_timestamp=1624607980603&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fget-func-name%2Fdownload%2Fget-func-name-2.0.0.tgz", "integrity": "sha1-6td0q+5y4gQJQzoGY2YCPdaIekE=", "dev": true}, "get-intrinsic": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/get-intrinsic/download/get-intrinsic-1.1.1.tgz", "integrity": "sha1-FfWfN2+FXERpY5SPDSTNNje0q8Y=", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "get-port": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/get-port/download/get-port-3.2.0.tgz?cache=0&sync_timestamp=1633281808408&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fget-port%2Fdownload%2Fget-port-3.2.0.tgz", "integrity": "sha1-3Xzn3hh8Bsi/NTeWrHHgmfCYDrw=", "dev": true}, "get-stream": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "requires": {"pump": "^3.0.0"}, "dependencies": {"pump": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "getpass": {"version": "0.1.7", "resolved": "https://registry.npmmirror.com/getpass/-/getpass-0.1.7.tgz", "integrity": "sha512-0fzj9JxOLfJ+XGLhR8ze3unN0KZCgZwiSSDz168VERjK8Wl8kVSdcu2kspd4s4wtAa1y/qrVRiAA0WclVsu0ng==", "requires": {"assert-plus": "^1.0.0"}}, "github-from-package": {"version": "0.0.0", "resolved": "https://registry.npmmirror.com/github-from-package/download/github-from-package-0.0.0.tgz", "integrity": "sha1-l/tdlr/eiXMxPyDoKI75oWf6ZM4=", "optional": true}, "glob": {"version": "7.2.0", "resolved": "https://registry.npmmirror.com/glob/download/glob-7.2.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.2.0.tgz", "integrity": "sha1-0VU1r3cy4C6Uj0xBYovZECk/YCM=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "global-dirs": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/global-dirs/download/global-dirs-3.0.0.tgz", "integrity": "sha1-cKdv6E6jFas3sfVXbL3n1I73JoY=", "dev": true, "requires": {"ini": "2.0.0"}, "dependencies": {"ini": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ini/download/ini-2.0.0.tgz", "integrity": "sha1-5f1Vbs3VcmvpePoQAYYurLCpS8U=", "dev": true}}}, "google-libphonenumber": {"version": "3.2.25", "resolved": "https://registry.npmmirror.com/google-libphonenumber/download/google-libphonenumber-3.2.25.tgz", "integrity": "sha1-O8GgQfo5JznSAerPZnYd8jbxYUs="}, "got": {"version": "9.6.0", "resolved": "https://registry.npmmirror.com/got/download/got-9.6.0.tgz", "integrity": "sha1-7fRefWf5lUVwXeH3u+7rEhdl7YU=", "dev": true, "requires": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}}, "graceful-fs": {"version": "4.2.8", "resolved": "https://registry.npmmirror.com/graceful-fs/download/graceful-fs-4.2.8.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fgraceful-fs%2Fdownload%2Fgraceful-fs-4.2.8.tgz", "integrity": "sha1-5BK40z9eAGWTy9PO5t+fLOu+gCo="}, "har-schema": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha512-Oqluz6zhGX8cyRaTQlFMPw80bSJVG2x/cFb8ZPhUILGgHka9SsokCCOQgpveePerqidZOrT14ipqfJb7ILcW5Q=="}, "har-validator": {"version": "5.1.5", "resolved": "https://registry.npmmirror.com/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/has-flag/download/has-flag-3.0.0.tgz?cache=0&sync_timestamp=1626715907927&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-flag%2Fdownload%2Fhas-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "has-symbols": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/has-symbols/download/has-symbols-1.0.2.tgz?cache=0&sync_timestamp=1614443557459&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-symbols%2Fdownload%2Fhas-symbols-1.0.2.tgz", "integrity": "sha1-Fl0wcMADCXUqEjakeTMeOsVvFCM="}, "has-tostringtag": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/has-tostringtag/download/has-tostringtag-1.0.0.tgz", "integrity": "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=", "requires": {"has-symbols": "^1.0.2"}}, "has-unicode": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/has-unicode/download/has-unicode-2.0.1.tgz", "integrity": "sha1-4Ob+aijPUROIVeCG0Wkedx3iqLk=", "optional": true}, "has-yarn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/has-yarn/download/has-yarn-2.1.0.tgz?cache=0&sync_timestamp=1631298711761&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhas-yarn%2Fdownload%2Fhas-yarn-2.1.0.tgz", "integrity": "sha1-E34RNUp7W/EapctknPDG8/8rLnc=", "dev": true}, "hoek": {"version": "6.1.3", "resolved": "https://registry.npmmirror.com/hoek/download/hoek-6.1.3.tgz", "integrity": "sha1-c7fTOVLgH+J6OLBFcpS3ndjaJCw="}, "hosted-git-info": {"version": "2.8.9", "resolved": "https://registry.npmmirror.com/hosted-git-info/download/hosted-git-info-2.8.9.tgz", "integrity": "sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=", "dev": true}, "http-assert": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/http-assert/download/http-assert-1.5.0.tgz", "integrity": "sha1-w4nM2HrBbtLfpiRv1zuSaqAOa48=", "requires": {"deep-equal": "~1.0.1", "http-errors": "~1.8.0"}}, "http-cache-semantics": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/http-cache-semantics/download/http-cache-semantics-4.1.0.tgz", "integrity": "sha1-SekcXL82yblLz81xwj1SSex045A=", "dev": true}, "http-errors": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.8.0.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.8.0.tgz", "integrity": "sha1-ddG75JfhBE9R5O6ecEpi8o0zZQc=", "requires": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}, "dependencies": {"depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}}}, "http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmmirror.com/http-proxy/download/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "requires": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}}, "http-signature": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha512-CAbnr6Rz4CYQkLYUtSNXxQPUH2gK8f3iWexVlsnMeD+GjlsQ0Xsy1cOX+mN3dtxYomRy21CiOzU8Uhw6OwncEQ==", "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmmirror.com/iconv-lite/download/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore-by-default": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/ignore-by-default/download/ignore-by-default-1.0.1.tgz", "integrity": "sha1-SMptcvbGo68Aqa1K5odr44ieKwk=", "dev": true}, "import-lazy": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/import-lazy/download/import-lazy-2.1.0.tgz", "integrity": "sha1-BWmOPUXIjo1+nZLLBYTnfwlvPkM=", "dev": true}, "imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true}, "inflation": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/inflation/download/inflation-2.0.0.tgz", "integrity": "sha1-i0F+R8KPklpFEz2RTKH9OJEH8w8="}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w="}, "ini": {"version": "1.3.8", "resolved": "https://registry.npmmirror.com/ini/download/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "devOptional": true}, "invert-kv": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/invert-kv/download/invert-kv-2.0.0.tgz", "integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI="}, "is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/is-binary-path/download/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "requires": {"binary-extensions": "^2.0.0"}}, "is-buffer": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/is-buffer/download/is-buffer-2.0.5.tgz?cache=0&sync_timestamp=1604429876103&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-buffer%2Fdownload%2Fis-buffer-2.0.5.tgz", "integrity": "sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE="}, "is-ci": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-ci/download/is-ci-2.0.0.tgz?cache=0&sync_timestamp=1618847026826&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-ci%2Fdownload%2Fis-ci-2.0.0.tgz", "integrity": "sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=", "dev": true, "requires": {"ci-info": "^2.0.0"}}, "is-core-module": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/is-core-module/download/is-core-module-2.8.0.tgz", "integrity": "sha1-AyEzbD0JJeSX/Zf12VyxFKXM1Ug=", "requires": {"has": "^1.0.3"}}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "requires": {"number-is-nan": "^1.0.0"}}, "is-generator-function": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/is-generator-function/download/is-generator-function-1.0.10.tgz", "integrity": "sha1-8VWLrxrBfg3up8BBXEODUf8rPHI=", "requires": {"has-tostringtag": "^1.0.0"}}, "is-glob": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-installed-globally": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/is-installed-globally/download/is-installed-globally-0.4.0.tgz", "integrity": "sha1-mg/UB5ScMPhutpWe8beZTtC3tSA=", "dev": true, "requires": {"global-dirs": "^3.0.0", "is-path-inside": "^3.0.2"}}, "is-npm": {"version": "5.0.0", "resolved": "https://registry.npmmirror.com/is-npm/download/is-npm-5.0.0.tgz", "integrity": "sha1-Q+jWXMVuG2f41HJiz2ZwmRk/Rag=", "dev": true}, "is-number": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true}, "is-obj": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-obj/download/is-obj-2.0.0.tgz", "integrity": "sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=", "dev": true}, "is-path-inside": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/is-path-inside/download/is-path-inside-3.0.3.tgz?cache=0&sync_timestamp=1620046845369&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fis-path-inside%2Fdownload%2Fis-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "dev": true}, "is-stream": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ="}, "is-typedarray": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo="}, "is-utf8": {"version": "0.2.1", "resolved": "https://registry.npmmirror.com/is-utf8/download/is-utf8-0.2.1.tgz", "integrity": "sha1-Sw2hRCEE0bM2NA6AeX6GXPOffXI=", "dev": true}, "is-yarn-global": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/is-yarn-global/download/is-yarn-global-0.3.0.tgz", "integrity": "sha1-1QLTOCWQ6jAEiTdGdUyJE5lz4jI=", "dev": true}, "isarray": {"version": "0.0.1", "resolved": "https://registry.npmmirror.com/isarray/download/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "isemail": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/isemail/download/isemail-3.2.0.tgz", "integrity": "sha1-WTEKAhkxqfsGu7UeFVzgs/I2gyw=", "requires": {"punycode": "2.x.x"}}, "isexe": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isstream": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/isstream/-/isstream-0.1.2.tgz", "integrity": "sha512-Yljz7ffyPbrLpLngrMtZ7NduUgVvi6wG9RJ9IUcyCd59YQ911PBJphODUcbOVbqYfxe1wuYf/LJ8PauMRwsM/g=="}, "jasmine": {"version": "3.10.0", "resolved": "https://registry.npmmirror.com/jasmine/download/jasmine-3.10.0.tgz", "integrity": "sha1-rNPNVgqdINj9rWvS3QWGfRiFA/M=", "dev": true, "requires": {"glob": "^7.1.6", "jasmine-core": "~3.10.0"}}, "jasmine-core": {"version": "3.10.0", "resolved": "https://registry.npmmirror.com/jasmine-core/download/jasmine-core-3.10.0.tgz", "integrity": "sha1-Hj5AU9lU620L+7PrhZuyGlZV3kU=", "dev": true}, "jasmine-ts": {"version": "0.3.3", "resolved": "https://registry.npmmirror.com/jasmine-ts/download/jasmine-ts-0.3.3.tgz", "integrity": "sha1-ommQtTN5yU33Bq3RWIdZP0ADKXQ=", "dev": true, "requires": {"yargs": "^16.2.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "cliui": {"version": "7.0.4", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-7.0.4.tgz", "integrity": "sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/get-caller-file/download/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-7.0.0.tgz", "integrity": "sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "y18n": {"version": "5.0.8", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-5.0.8.tgz", "integrity": "sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=", "dev": true}, "yargs": {"version": "16.2.0", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-16.2.0.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-16.2.0.tgz", "integrity": "sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=", "dev": true, "requires": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}}, "yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-20.2.9.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-20.2.9.tgz", "integrity": "sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=", "dev": true}}}, "joi": {"version": "14.3.1", "resolved": "https://registry.npmmirror.com/joi/download/joi-14.3.1.tgz", "integrity": "sha1-FkomLsC4VUZuDDXuoqiFrotscDw=", "requires": {"hoek": "6.x.x", "isemail": "3.x.x", "topo": "3.x.x"}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk="}, "js-yaml": {"version": "3.14.1", "resolved": "https://registry.npmmirror.com/js-yaml/download/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha512-UVU9dibq2JcFWxQPA6KCqj5O42VOmAY3zQUfEKxU0KpTGXwNoCjkX1e13eHNvw/xPynt6pU0rZ1htjWTNTSXsg=="}, "json-buffer": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/json-buffer/download/json-buffer-3.0.0.tgz", "integrity": "sha1-Wx85evx11ne96Lz8Dkfh+aPZqJg=", "dev": true}, "json-schema": {"version": "0.4.0", "resolved": "https://registry.npmmirror.com/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha512-es94M3nTIfsEPisRafak+HDLfHXnKBhV3vU5eqPcS3flIWqcxJWgXHXiey3YrpaNsanY5ei1VoYEbOzijuq9BA=="}, "json-schema-ref-parser": {"version": "5.1.3", "resolved": "https://registry.npmmirror.com/json-schema-ref-parser/download/json-schema-ref-parser-5.1.3.tgz", "integrity": "sha1-+GxYaPQImOaRaeG7yFRyWk/Q4a0=", "requires": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "js-yaml": "^3.12.0", "ono": "^4.0.6"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}}}, "json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmmirror.com/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stable-stringify": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/json-stable-stringify/download/json-stable-stringify-1.0.1.tgz", "integrity": "sha1-mnWdOcXy/1A/1TAGRu1EX4jE+a8=", "requires": {"jsonify": "~0.0.0"}}, "json-stringify-safe": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha512-ZClg6AaYvamvYEE82d3Iyd3vSSIjQ+odgjaTzRuO3s7toCdFKczob2i0zCh7JE8kWn17yvAWhUVxvqGwUalsRA=="}, "jsonfile": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/jsonfile/download/jsonfile-4.0.0.tgz?cache=0&sync_timestamp=1604161876665&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fjsonfile%2Fdownload%2Fjsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "requires": {"graceful-fs": "^4.1.6"}}, "jsonify": {"version": "0.0.0", "resolved": "https://registry.npmmirror.com/jsonify/download/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM="}, "jsonschema": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/jsonschema/download/jsonschema-1.2.4.tgz", "integrity": "sha1-pGusXTUGolRGW8VIh24mfG0NZGQ="}, "jsonschema-draft4": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/jsonschema-draft4/download/jsonschema-draft4-1.0.0.tgz", "integrity": "sha1-8K8gBQVPDwrefqIRhhS2ncUS2GU="}, "jsprim": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "kafka-node": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/kafka-node/download/kafka-node-4.1.3.tgz", "integrity": "sha1-nl/fOlViNw/w+CxUgb0UpuOa2T0=", "requires": {"async": "^2.6.2", "binary": "~0.3.0", "bl": "^2.2.0", "buffer-crc32": "~0.2.5", "buffermaker": "~1.2.0", "debug": "^2.1.3", "denque": "^1.3.0", "lodash": "^4.17.4", "minimatch": "^3.0.2", "nested-error-stacks": "^2.0.0", "optional": "^0.1.3", "retry": "^0.10.1", "snappy": "^6.0.1", "uuid": "^3.0.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "keygrip": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/keygrip/download/keygrip-1.1.0.tgz", "integrity": "sha1-hxsWgdXhWcYqRFsMdLYV4JF+ciY=", "requires": {"tsscmp": "1.0.6"}}, "keyv": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/keyv/download/keyv-3.1.0.tgz", "integrity": "sha1-7MIoSG9pmR5J6UdkhaW+Ho/FxNk=", "dev": true, "requires": {"json-buffer": "3.0.0"}}, "koa": {"version": "2.13.4", "resolved": "https://registry.npmmirror.com/koa/download/koa-2.13.4.tgz?cache=0&sync_timestamp=1634623957508&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fkoa%2Fdownload%2Fkoa-2.13.4.tgz", "integrity": "sha1-7lsMs54LgGnDjRFROcd0gz0yRi4=", "requires": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.8.0", "debug": "^4.3.2", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}}, "koa-body": {"version": "4.2.0", "resolved": "https://registry.npmmirror.com/koa-body/download/koa-body-4.2.0.tgz", "integrity": "sha1-NyKSCLggdhrKWCLRTF/FXO4xsm8=", "requires": {"@types/formidable": "^1.0.31", "co-body": "^5.1.1", "formidable": "^1.1.1"}}, "koa-compose": {"version": "4.1.0", "resolved": "https://registry.npmmirror.com/koa-compose/download/koa-compose-4.1.0.tgz", "integrity": "sha1-UHMGuTcZAdtBEhyBLpI9DWfT6Hc="}, "koa-convert": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/koa-convert/download/koa-convert-2.0.0.tgz", "integrity": "sha1-hqDETYHUBVG64i/uZwmQRXPupPU=", "requires": {"co": "^4.6.0", "koa-compose": "^4.1.0"}}, "koa-ejs": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/koa-ejs/download/koa-ejs-4.3.0.tgz", "integrity": "sha1-BthFmx1S9MQbQT2HePceGrLsUAk=", "requires": {"debug": "^2.6.1", "ejs": "^2.6.1", "mz": "^2.6.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "koa-etag": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/koa-etag/download/koa-etag-3.0.0.tgz", "integrity": "sha1-nvc4Ld1agqsN6xU0FckVg293HT8=", "requires": {"etag": "^1.3.0", "mz": "^2.1.0"}}, "koa-multer": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/koa-multer/-/koa-multer-1.0.2.tgz", "integrity": "sha512-0kFzN4atVd+9oiG+4fYxQ9S2T3dPhKNvmhITIY606Qn9wLEmfhW0DhSpOzRYhddN//4rh/TCK95TMtflmFa5lA==", "requires": {"multer": "1.3.0"}}, "koa-router": {"version": "7.4.0", "resolved": "https://registry.npmmirror.com/koa-router/download/koa-router-7.4.0.tgz?cache=0&sync_timestamp=1629648499991&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fkoa-router%2Fdownload%2Fkoa-router-7.4.0.tgz", "integrity": "sha1-ruH3rcAtXLMdfWdGXJ6syCXoxeA=", "requires": {"debug": "^3.1.0", "http-errors": "^1.3.1", "koa-compose": "^3.0.0", "methods": "^1.0.1", "path-to-regexp": "^1.1.1", "urijs": "^1.19.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}, "koa-compose": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/koa-compose/download/koa-compose-3.2.1.tgz", "integrity": "sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=", "requires": {"any-promise": "^1.1.0"}}}}, "koa-send": {"version": "4.1.3", "resolved": "https://registry.npmmirror.com/koa-send/download/koa-send-4.1.3.tgz", "integrity": "sha1-CCIge79SU6QUyPF2Xrwp+kE1PLY=", "requires": {"debug": "^2.6.3", "http-errors": "^1.6.1", "mz": "^2.6.0", "resolve-path": "^1.4.0"}, "dependencies": {"debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/download/debug-2.6.9.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "requires": {"ms": "2.0.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}}}, "latest-version": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/latest-version/download/latest-version-5.1.0.tgz", "integrity": "sha1-EZ3+kI/jjRXfpD7NE/oS7Igy+s4=", "dev": true, "requires": {"package-json": "^6.3.0"}}, "lcid": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/lcid/download/lcid-2.0.0.tgz", "integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "requires": {"invert-kv": "^2.0.0"}}, "load-json-file": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/load-json-file/download/load-json-file-1.1.0.tgz?cache=0&sync_timestamp=1631508525141&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fload-json-file%2Fdownload%2Fload-json-file-1.1.0.tgz", "integrity": "sha1-lWkFcI1YtLq0wiYbBPWfMcmTdMA=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "parse-json": "^2.2.0", "pify": "^2.0.0", "pinkie-promise": "^2.0.0", "strip-bom": "^2.0.0"}}, "locate-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "requires": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}}, "lodash": {"version": "4.17.21", "resolved": "https://registry.npmmirror.com/lodash/download/lodash-4.17.21.tgz?cache=0&sync_timestamp=1618847150612&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flodash%2Fdownload%2Flodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.get": {"version": "4.4.2", "resolved": "https://registry.npmmirror.com/lodash.get/download/lodash.get-4.4.2.tgz", "integrity": "sha1-LRd/ZS+jHpObRDjVNBSZ36OCXpk="}, "lodash.isequal": {"version": "4.5.0", "resolved": "https://registry.npmmirror.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz", "integrity": "sha1-QVxEePK8wwEgwizhDtMib30+GOA="}, "log4js": {"version": "4.5.1", "resolved": "https://registry.npmmirror.com/log4js/download/log4js-4.5.1.tgz", "integrity": "sha1-5UNiXpfZ5vPm58n8GW3WqyyuMLU=", "requires": {"date-format": "^2.0.0", "debug": "^4.1.1", "flatted": "^2.0.0", "rfdc": "^1.1.4", "streamroller": "^1.0.6"}}, "long": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/long/download/long-1.1.2.tgz", "integrity": "sha1-6u9ZUcp1UdlpJrgtokLbnWso+1M="}, "lowercase-keys": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/lowercase-keys/download/lowercase-keys-1.0.1.tgz?cache=0&sync_timestamp=1634551808987&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Flowercase-keys%2Fdownload%2Flowercase-keys-1.0.1.tgz", "integrity": "sha1-b54wtHCE2XGnyCD/FabFFnt0wm8=", "dev": true}, "lru-cache": {"version": "4.1.5", "resolved": "https://registry.npmmirror.com/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "requires": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "make-dir": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/make-dir/download/make-dir-3.1.0.tgz", "integrity": "sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=", "dev": true, "requires": {"semver": "^6.0.0"}}, "make-error": {"version": "1.3.6", "resolved": "https://registry.npmmirror.com/make-error/download/make-error-1.3.6.tgz", "integrity": "sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=", "dev": true}, "map-age-cleaner": {"version": "0.1.3", "resolved": "https://registry.npmmirror.com/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz?cache=0&sync_timestamp=1629750638543&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmap-age-cleaner%2Fdownload%2Fmap-age-cleaner-0.1.3.tgz", "integrity": "sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=", "requires": {"p-defer": "^1.0.0"}}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmmirror.com/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "mem": {"version": "4.3.0", "resolved": "https://registry.npmmirror.com/mem/download/mem-4.3.0.tgz", "integrity": "sha1-Rhr0l7xK4JYIzbLmDu+2m/90QXg=", "requires": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "mime": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/mime/download/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true}, "mime-db": {"version": "1.50.0", "resolved": "https://registry.npmmirror.com/mime-db/download/mime-db-1.50.0.tgz?cache=0&sync_timestamp=1631863129751&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-db%2Fdownload%2Fmime-db-1.50.0.tgz", "integrity": "sha1-q9SslOmNPA4YUBbGerRdX95AwR8="}, "mime-types": {"version": "2.1.33", "resolved": "https://registry.npmmirror.com/mime-types/download/mime-types-2.1.33.tgz?cache=0&sync_timestamp=1633108241037&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmime-types%2Fdownload%2Fmime-types-2.1.33.tgz", "integrity": "sha1-H6EqkERy+v0GjkjZ6EAfdNP3Dts=", "requires": {"mime-db": "1.50.0"}}, "mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/mimic-fn/download/mimic-fn-2.1.0.tgz", "integrity": "sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs="}, "mimic-response": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/mimic-response/download/mimic-response-1.0.1.tgz", "integrity": "sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=", "devOptional": true}, "minimatch": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/minimatch/download/minimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.5", "resolved": "https://registry.npmmirror.com/minimist/download/minimist-1.2.5.tgz", "integrity": "sha1-Z9ZgFLZqaoqqDAg8X9WN9OTpdgI="}, "mkdirp": {"version": "0.5.5", "resolved": "https://registry.npmmirror.com/mkdirp/download/mkdirp-0.5.5.tgz", "integrity": "sha1-2Rzv1i0UNsoPQWIOJRKI1CAJne8=", "requires": {"minimist": "^1.2.5"}}, "moment": {"version": "2.29.1", "resolved": "https://registry.npmmirror.com/moment/download/moment-2.29.1.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fmoment%2Fdownload%2Fmoment-2.29.1.tgz", "integrity": "sha1-sr52n6MZQL6e7qZGnAdeNQBvo9M="}, "moment-timezone": {"version": "0.5.33", "resolved": "https://registry.npmmirror.com/moment-timezone/download/moment-timezone-0.5.33.tgz", "integrity": "sha1-slL9a7V/NBybWaWrYajlGnO70iw=", "requires": {"moment": ">= 2.9.0"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/ms/download/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk="}, "multer": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/multer/-/multer-1.3.0.tgz", "integrity": "sha512-wbAkTsh0QXkvqvHCU2qSLEXLuRN7IKMEe80+JrXfJzANniPNgrNcDOMKfGgR1EhL7y7MHIbODVwT7uaVY20ggw==", "requires": {"append-field": "^0.1.0", "busboy": "^0.2.11", "concat-stream": "^1.5.0", "mkdirp": "^0.5.1", "object-assign": "^3.0.0", "on-finished": "^2.3.0", "type-is": "^1.6.4", "xtend": "^4.0.0"}, "dependencies": {"object-assign": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/object-assign/-/object-assign-3.0.0.tgz", "integrity": "sha512-jHP15vXVGeVh1HuaA2wY6lxk+whK/x4KBG88VXeRma7CCun7iGD5qPc4eYykQ9sdQvg8jkwFKsSxHln2ybW3xQ=="}}}, "mz": {"version": "2.7.0", "resolved": "https://registry.npmmirror.com/mz/download/mz-2.7.0.tgz", "integrity": "sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=", "requires": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "nan": {"version": "2.15.0", "resolved": "https://registry.npmmirror.com/nan/download/nan-2.15.0.tgz", "integrity": "sha1-PzSkc/8Y4VwbVia2KQO1rW5mX+4=", "optional": true}, "napi-build-utils": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/napi-build-utils/download/napi-build-utils-1.0.2.tgz", "integrity": "sha1-sf3cCyxG44Cgt6dvmE3UfEGhOAY=", "optional": true}, "negotiator": {"version": "0.6.2", "resolved": "https://registry.npmmirror.com/negotiator/download/negotiator-0.6.2.tgz", "integrity": "sha1-/qz3zPUlp3rpY0Q2pkiD/+yjRvs="}, "nested-error-stacks": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/nested-error-stacks/download/nested-error-stacks-2.1.0.tgz", "integrity": "sha1-D73PPhP+SZR4EoBST4uWsM3/nGE="}, "nice-try": {"version": "1.0.5", "resolved": "https://registry.npmmirror.com/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y="}, "node-abi": {"version": "2.30.1", "resolved": "https://registry.npmmirror.com/node-abi/download/node-abi-2.30.1.tgz?cache=0&sync_timestamp=1632784314415&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-abi%2Fdownload%2Fnode-abi-2.30.1.tgz", "integrity": "sha1-xDfUsf4OKFqvKQ1FtF1Nev7axM8=", "optional": true, "requires": {"semver": "^5.4.1"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "optional": true}}}, "node-fetch": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/node-fetch/-/node-fetch-1.3.3.tgz", "integrity": "sha512-aru9GLSvU8scV9SMrOa0AiPF8Rst8mSMaN51GGxMlTN3yL7zybAYjHSVefVOmurcGj2pdF0F1Qy8Nekzqbk+/Q==", "requires": {"encoding": "^0.1.11"}}, "node-object-hash": {"version": "1.4.2", "resolved": "https://registry.npmmirror.com/node-object-hash/download/node-object-hash-1.4.2.tgz?cache=0&sync_timestamp=1631619359445&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnode-object-hash%2Fdownload%2Fnode-object-hash-1.4.2.tgz", "integrity": "sha1-OFgz2FsimQK3WCYiT2B3vpaanpQ="}, "nodemon": {"version": "2.0.14", "resolved": "https://registry.npmmirror.com/nodemon/download/nodemon-2.0.14.tgz?cache=0&sync_timestamp=1634655502807&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnodemon%2Fdownload%2Fnodemon-2.0.14.tgz", "integrity": "sha1-KHx6L2zYoYsH6UzXduy2qC5LpDk=", "dev": true, "requires": {"chokidar": "^3.2.2", "debug": "^3.2.6", "ignore-by-default": "^1.0.1", "minimatch": "^3.0.4", "pstree.remy": "^1.1.7", "semver": "^5.7.1", "supports-color": "^5.5.0", "touch": "^3.1.0", "undefsafe": "^2.0.3", "update-notifier": "^5.1.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "requires": {"ms": "^2.1.1"}}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}}}, "noop-logger": {"version": "0.1.1", "resolved": "https://registry.npmmirror.com/noop-logger/download/noop-logger-0.1.1.tgz", "integrity": "sha1-lKKxYzxPExdVMAfYlm/Q6EG2pMI=", "optional": true}, "nopt": {"version": "1.0.10", "resolved": "https://registry.npmmirror.com/nopt/download/nopt-1.0.10.tgz?cache=0&sync_timestamp=1624607881839&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnopt%2Fdownload%2Fnopt-1.0.10.tgz", "integrity": "sha1-bd0hvSoxQXuScn3Vhfim83YI6+4=", "dev": true, "requires": {"abbrev": "1"}}, "normalize-package-data": {"version": "2.5.0", "resolved": "https://registry.npmmirror.com/normalize-package-data/download/normalize-package-data-2.5.0.tgz?cache=0&sync_timestamp=1629301911873&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnormalize-package-data%2Fdownload%2Fnormalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "requires": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}, "dependencies": {"semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc=", "dev": true}}}, "normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true}, "normalize-url": {"version": "4.5.1", "resolved": "https://registry.npmmirror.com/normalize-url/download/normalize-url-4.5.1.tgz", "integrity": "sha1-DdkM8SiO4dExO4cIHJpZMu5IUYo=", "dev": true}, "nos-node-sdk": {"version": "0.0.4", "resolved": "https://registry.npmmirror.com/nos-node-sdk/-/nos-node-sdk-0.0.4.tgz", "integrity": "sha512-Jw3yzGfNscuIuigFWNawI/2KD4UcxJlm/2hr3BEeVD9709WkadTDFc2a+7HyEKbLU9TnEWR/CLqcbCCjI3Ciyw==", "requires": {"node-fetch": "~1.3.3", "q": "1.4.1", "superagent": "~1.4.0", "validator": "5.5.0", "xml2js": "~0.4.17"}, "dependencies": {"async": {"version": "0.9.2", "resolved": "https://registry.npmmirror.com/async/-/async-0.9.2.tgz", "integrity": "sha512-l6ToIJIotphWahxxHyzK9bnLR6kM4jJIIgLShZeqLY7iboHoGkdgFl7W2/Ivi4SkMJYGKqW8vSuk0uKUj6qsSw=="}, "combined-stream": {"version": "0.0.7", "resolved": "https://registry.npmmirror.com/combined-stream/-/combined-stream-0.0.7.tgz", "integrity": "sha512-qfexlmLp9MyrkajQVyjEDb0Vj+KhRgR/rxLiVhaihlT+ZkX0lReqtH6Ack40CvMDERR4b5eFp3CreskpBs1Pig==", "requires": {"delayed-stream": "0.0.5"}}, "component-emitter": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/component-emitter/-/component-emitter-1.1.2.tgz", "integrity": "sha512-YhIbp3PJiznERfjlIkK0ue4obZxt2S60+0W8z24ZymOHT8sHloOqWOqZRU2eN5OlY8U08VFsP02letcu26FilA=="}, "cookiejar": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/cookiejar/-/cookiejar-2.0.1.tgz", "integrity": "sha512-Txnl7P7okmx/FyZNRAjPyHMKISV2ADNbd+xITouEVyl2jUczrU4tJT40KcfQL/ifCo0kqqLgD49QlNofAAmBKQ=="}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmmirror.com/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "delayed-stream": {"version": "0.0.5", "resolved": "https://registry.npmmirror.com/delayed-stream/-/delayed-stream-0.0.5.tgz", "integrity": "sha512-v+7uBd1pqe5YtgPacIIbZ8HuHeLFVNe4mUEyFDXL6KiqzEykjbw+5mXZXpGFgNVasdL4jWKgaKIXrEHiynN1LA=="}, "extend": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/extend/-/extend-1.2.1.tgz", "integrity": "sha512-2/JwIYRpMBDSjbQjUUppNSrmc719crhFaWIdT+TRSVA8gE+6HEobQWqJ6VkPt/H8twS7h/0WWs7veh8wmp98Ng=="}, "form-data": {"version": "0.2.0", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-0.2.0.tgz", "integrity": "sha512-LkinaG6JazVhYj2AKi67NOIAhqXcBOQACraT0WdhWW4ZO3kTiS0X7C1nJ1jFZf6wak4bVHIA/oOzWkh2ThAipg==", "requires": {"async": "~0.9.0", "combined-stream": "~0.0.4", "mime-types": "~2.0.3"}}, "formidable": {"version": "1.0.14", "resolved": "https://registry.npmmirror.com/formidable/-/formidable-1.0.14.tgz", "integrity": "sha512-aOskFHEfYwkSKSzGui5jhQ+uyLo2NTwpzhndggz2YZHlv0HkAi+zG5ZEBCL3GTvqLyr/FzX9Mvx9DueCmu2HzQ=="}, "methods": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/methods/-/methods-1.0.1.tgz", "integrity": "sha512-2403MfnVypWSNIEpmQ26/ObZ5kSUx37E8NHRvriw0+I8Sne7k0HGuLGCk0OrCqURh4UIygD0cSsYq+Ll+kzNqA=="}, "mime": {"version": "1.3.4", "resolved": "https://registry.npmmirror.com/mime/-/mime-1.3.4.tgz", "integrity": "sha512-sAaYXszED5ALBt665F0wMQCUXpGuZsGdopoqcHPdL39ZYdi7uHoZlhrfZfhv8WzivhBzr/oXwaj+yiK5wY8MXQ=="}, "mime-db": {"version": "1.12.0", "resolved": "https://registry.npmmirror.com/mime-db/-/mime-db-1.12.0.tgz", "integrity": "sha512-5aMAW7I4jZoZB27fXRuekqc4DVvJ7+hM8UcWrNj2mqibE54gXgPSonBYBdQW5hyaVNGmiYjY0ZMqn9fBefWYvA=="}, "mime-types": {"version": "2.0.14", "resolved": "https://registry.npmmirror.com/mime-types/-/mime-types-2.0.14.tgz", "integrity": "sha512-2ZHUEstNkIf2oTWgtODr6X0Cc4Ns/RN/hktdozndiEhhAC2wxXejF1FH0XLHTEImE9h6gr/tcnr3YOnSGsxc7Q==", "requires": {"mime-db": "~1.12.0"}}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A=="}, "qs": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/qs/-/qs-2.3.3.tgz", "integrity": "sha512-f5M0HQqZWkzU8GELTY8LyMrGkr3bPjKoFtTkwUEqJQbcljbeK8M7mliP9Ia2xoOI6oMerp+QPS7oYJtpGmWe/A=="}, "readable-stream": {"version": "1.0.27-1", "resolved": "https://registry.npmmirror.com/readable-stream/-/readable-stream-1.0.27-1.tgz", "integrity": "sha512-uQE31HGhpMrqZwtDjRliOs2aC3XBi+DdkhLs+Xa0dvVD5eDiZr3+k8rKVZcyTzxosgtMw7B/twQsK3P1KTZeVg==", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "string_decoder": {"version": "0.10.31", "resolved": "https://registry.npmmirror.com/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha512-ev2QzSzWPYmy9GuqfIVildA4OdcGLeFZQrq5ys6RtiuF+RQQiZWr8TZNyAcuVXyQRYfEO+MsoB/1BuQVhOJuoQ=="}, "superagent": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/superagent/-/superagent-1.4.0.tgz", "integrity": "sha512-OxU2FOnlgbjhRP0jvRAOz3E9p0FYxT+zkkBL81d9HwZ9VAMUR1JAnQFFNHroRnh3royOkqraV1adJ2J99VDC3w==", "requires": {"component-emitter": "1.1.2", "cookiejar": "2.0.1", "debug": "2", "extend": "1.2.1", "form-data": "0.2.0", "formidable": "1.0.14", "methods": "1.0.1", "mime": "1.3.4", "qs": "2.3.3", "readable-stream": "1.0.27-1", "reduce-component": "1.0.1"}}, "validator": {"version": "5.5.0", "resolved": "https://registry.npmmirror.com/validator/-/validator-5.5.0.tgz", "integrity": "sha512-r1y27o0tNMjLbGSjjiX2xTdxKT1HV/hO0iwy5yN/du3jT68r8sXr047p7bwh8jv0YaIX/12a2+D9y8XfRD8VIw=="}}}, "npm-run-path": {"version": "2.0.2", "resolved": "https://registry.npmmirror.com/npm-run-path/download/npm-run-path-2.0.2.tgz?cache=0&sync_timestamp=1633420549182&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fnpm-run-path%2Fdownload%2Fnpm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "requires": {"path-key": "^2.0.0"}}, "npmlog": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/npmlog/download/npmlog-4.1.2.tgz", "integrity": "sha1-CKfyqL9zRgR3mp76StXMcXq7lUs=", "optional": true, "requires": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "number-is-nan": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/number-is-nan/download/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0="}, "oauth-sign": {"version": "0.9.0", "resolved": "https://registry.npmmirror.com/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ=="}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmmirror.com/object-assign/download/object-assign-4.1.1.tgz?cache=0&sync_timestamp=1618847240432&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fobject-assign%2Fdownload%2Fobject-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-inspect": {"version": "1.11.0", "resolved": "https://registry.npmmirror.com/object-inspect/download/object-inspect-1.11.0.tgz", "integrity": "sha1-nc6xRs7dQUig2eUauI00z1CZIrE="}, "object-keys": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/object-keys/download/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true}, "object.assign": {"version": "4.1.2", "resolved": "https://registry.npmmirror.com/object.assign/download/object.assign-4.1.2.tgz", "integrity": "sha1-DtVKNC7Os3s4/3brgxoOeIy2OUA=", "dev": true, "requires": {"call-bind": "^1.0.0", "define-properties": "^1.1.3", "has-symbols": "^1.0.1", "object-keys": "^1.1.1"}}, "on-finished": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/on-finished/download/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "requires": {"ee-first": "1.1.1"}}, "once": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "only": {"version": "0.0.2", "resolved": "https://registry.npmmirror.com/only/download/only-0.0.2.tgz", "integrity": "sha1-Kv3oTQPlC5qO3EROMGEKcCle37Q="}, "ono": {"version": "4.0.11", "resolved": "https://registry.npmmirror.com/ono/download/ono-4.0.11.tgz", "integrity": "sha1-x/Qgmz45bopE70O5ztx/XXkdIh0=", "requires": {"format-util": "^1.0.3"}}, "openapi-schema-validation": {"version": "0.4.2", "resolved": "https://registry.npmmirror.com/openapi-schema-validation/download/openapi-schema-validation-0.4.2.tgz", "integrity": "sha1-iVwpAhvgLgAPccUfhZ2lIRjrHiE=", "requires": {"jsonschema": "1.2.4", "jsonschema-draft4": "^1.0.0", "swagger-schema-official": "2.0.0-bab6bed"}}, "optional": {"version": "0.1.4", "resolved": "https://registry.npmmirror.com/optional/download/optional-0.1.4.tgz", "integrity": "sha1-zbGpvtxzfSAl9pDO61DgSURP1bM="}, "os-homedir": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/os-homedir/download/os-homedir-1.0.2.tgz", "integrity": "sha1-/7xJiDNuDoM94MFox+8VISGqf7M=", "optional": true}, "os-locale": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/os-locale/download/os-locale-3.1.0.tgz?cache=0&sync_timestamp=1633618411079&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-3.1.0.tgz", "integrity": "sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=", "requires": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}}, "p-cancelable": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/p-cancelable/download/p-cancelable-1.1.0.tgz", "integrity": "sha1-0HjRWjr0CSIMiG8dmgyi5EGrJsw=", "dev": true}, "p-defer": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/p-defer/download/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww="}, "p-finally": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/p-finally/download/p-finally-1.0.0.tgz?cache=0&sync_timestamp=1617947695861&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-finally%2Fdownload%2Fp-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4="}, "p-is-promise": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/p-is-promise/download/p-is-promise-2.1.0.tgz", "integrity": "sha1-kYzrrqJIpiz3/6uOO8qMX4gvxC4="}, "p-limit": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/p-limit/download/p-limit-2.3.0.tgz", "integrity": "sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=", "requires": {"p-try": "^2.0.0"}}, "p-locate": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/p-locate/download/p-locate-3.0.0.tgz?cache=0&sync_timestamp=1629892721671&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fp-locate%2Fdownload%2Fp-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "requires": {"p-limit": "^2.0.0"}}, "p-try": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/p-try/download/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY="}, "package-json": {"version": "6.5.0", "resolved": "https://registry.npmmirror.com/package-json/download/package-json-6.5.0.tgz", "integrity": "sha1-b+7ayjXnVyWHbQsOZJdGl/7RRbA=", "dev": true, "requires": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}}, "parse-json": {"version": "2.2.0", "resolved": "https://registry.npmmirror.com/parse-json/download/parse-json-2.2.0.tgz", "integrity": "sha1-9ID0BDTvgHQfhGkJn43qGPVaTck=", "dev": true, "requires": {"error-ex": "^1.2.0"}}, "parseurl": {"version": "1.3.3", "resolved": "https://registry.npmmirror.com/parseurl/download/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ="}, "path-exists": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU="}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-key": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A="}, "path-parse": {"version": "1.0.7", "resolved": "https://registry.npmmirror.com/path-parse/download/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "path-to-regexp": {"version": "1.8.0", "resolved": "https://registry.npmmirror.com/path-to-regexp/download/path-to-regexp-1.8.0.tgz?cache=0&sync_timestamp=1618847046445&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpath-to-regexp%2Fdownload%2Fpath-to-regexp-1.8.0.tgz", "integrity": "sha1-iHs7qdhDk+h6CgufTLdWGYtTVIo=", "requires": {"isarray": "0.0.1"}}, "path-type": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/path-type/download/path-type-1.1.0.tgz", "integrity": "sha1-WcRPfuSR2nBNpBXaWkBwuk+P5EE=", "dev": true, "requires": {"graceful-fs": "^4.1.2", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "pathval": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/pathval/download/pathval-1.1.1.tgz", "integrity": "sha1-hTTnenfOesWiUS6iHg/bj89sPY0=", "dev": true}, "performance-now": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha512-7EAHlyLHI56VEIdK57uwHdHKIaAGbnXPiw0yWbarQZOKaKpvUIgW0jWRVLiatnM+XXlSwsanIBH/hzGMJulMow=="}, "picomatch": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/picomatch/download/picomatch-2.3.0.tgz?cache=0&sync_timestamp=1621648246651&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fpicomatch%2Fdownload%2Fpicomatch-2.3.0.tgz", "integrity": "sha1-8fBh3o9qS/AiiS4tEoI0+5gwKXI=", "dev": true}, "pify": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true}, "pinkie": {"version": "2.0.4", "resolved": "https://registry.npmmirror.com/pinkie/download/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true}, "pinkie-promise": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "requires": {"pinkie": "^2.0.0"}}, "prebuild-install": {"version": "5.3.0", "resolved": "https://registry.npmmirror.com/prebuild-install/download/prebuild-install-5.3.0.tgz?cache=0&sync_timestamp=1628702746955&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fprebuild-install%2Fdownload%2Fprebuild-install-5.3.0.tgz", "integrity": "sha1-WLTYNE4DWQmQkx7giN1UAbAwBMg=", "optional": true, "requires": {"detect-libc": "^1.0.3", "expand-template": "^2.0.3", "github-from-package": "0.0.0", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "napi-build-utils": "^1.0.1", "node-abi": "^2.7.0", "noop-logger": "^0.1.1", "npmlog": "^4.0.1", "os-homedir": "^1.0.1", "pump": "^2.0.1", "rc": "^1.2.7", "simple-get": "^2.7.0", "tar-fs": "^1.13.0", "tunnel-agent": "^0.6.0", "which-pm-runs": "^1.0.0"}}, "prepend-http": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/prepend-http/download/prepend-http-2.0.0.tgz?cache=0&sync_timestamp=1628547565904&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fprepend-http%2Fdownload%2Fprepend-http-2.0.0.tgz", "integrity": "sha1-6SQ0v6XqjBn0HN/UAddBo8gZ2Jc=", "dev": true}, "process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/process-nextick-args/download/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I="}, "pseudomap": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/pseudomap/download/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM="}, "psl": {"version": "1.9.0", "resolved": "https://registry.npmmirror.com/psl/-/psl-1.9.0.tgz", "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag=="}, "pstree.remy": {"version": "1.1.8", "resolved": "https://registry.npmmirror.com/pstree.remy/download/pstree.remy-1.1.8.tgz", "integrity": "sha1-wkIiT0pnwh9oaDm720rCgrg3PTo=", "dev": true}, "pump": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/pump/download/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "optional": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "punycode": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew="}, "pupa": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/pupa/download/pupa-2.1.1.tgz", "integrity": "sha1-9ej9SvwsXZeCj6pSNUnth0SiDWI=", "dev": true, "requires": {"escape-goat": "^2.0.0"}}, "q": {"version": "1.4.1", "resolved": "https://registry.npmmirror.com/q/-/q-1.4.1.tgz", "integrity": "sha512-/CdEdaw49VZVmyIDGUQKDDT53c7qBkO6g5CefWz91Ae+l4+cRtcDYwMTXh6me4O8TMldeGHG3N2Bl84V78Ywbg=="}, "qs": {"version": "6.10.1", "resolved": "https://registry.npmmirror.com/qs/download/qs-6.10.1.tgz", "integrity": "sha1-STFIL6jWR6Wqt5nFJx0hM7mB+2o=", "requires": {"side-channel": "^1.0.4"}}, "raw-body": {"version": "2.4.1", "resolved": "https://registry.npmmirror.com/raw-body/download/raw-body-2.4.1.tgz", "integrity": "sha1-MKyC+Yu1rowVLmcUnayNVRU7Fow=", "requires": {"bytes": "3.1.0", "http-errors": "1.7.3", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "dependencies": {"depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "http-errors": {"version": "1.7.3", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.7.3.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.7.3.tgz", "integrity": "sha1-bGGeT5xgMIw4UZSYwU+7EKrOuwY=", "requires": {"depd": "~1.1.2", "inherits": "2.0.4", "setprototypeof": "1.1.1", "statuses": ">= 1.5.0 < 2", "toidentifier": "1.0.0"}}, "setprototypeof": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.1.tgz", "integrity": "sha1-fpWsskqpL1iF4KvvW6ExMw1K5oM="}}}, "rc": {"version": "1.2.8", "resolved": "https://registry.npmmirror.com/rc/download/rc-1.2.8.tgz", "integrity": "sha1-zZJL9SAKB1uDwYjNa54hG3/A0+0=", "devOptional": true, "requires": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}}, "read-pkg": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/read-pkg/download/read-pkg-1.1.0.tgz?cache=0&sync_timestamp=1628984695234&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fread-pkg%2Fdownload%2Fread-pkg-1.1.0.tgz", "integrity": "sha1-9f+qXs0pyzHAR0vKfXVra7KePyg=", "dev": true, "requires": {"load-json-file": "^1.0.0", "normalize-package-data": "^2.3.2", "path-type": "^1.0.0"}}, "read-pkg-up": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/read-pkg-up/download/read-pkg-up-1.0.1.tgz?cache=0&sync_timestamp=1634147799745&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fread-pkg-up%2Fdownload%2Fread-pkg-up-1.0.1.tgz", "integrity": "sha1-nWPBMnbAZZGNV/ACpX9AobZD+wI=", "dev": true, "requires": {"find-up": "^1.0.0", "read-pkg": "^1.0.0"}, "dependencies": {"find-up": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/find-up/download/find-up-1.1.2.tgz?cache=0&sync_timestamp=1633618631704&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ffind-up%2Fdownload%2Ffind-up-1.1.2.tgz", "integrity": "sha1-ay6YIrGizgpgq2TWEOzK1TyyTQ8=", "dev": true, "requires": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}}, "path-exists": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/path-exists/download/path-exists-2.1.0.tgz", "integrity": "sha1-D+tsZPD8UY2adU3V77YscCJ2H0s=", "dev": true, "requires": {"pinkie-promise": "^2.0.0"}}}}, "readable-stream": {"version": "2.3.7", "resolved": "https://registry.npmmirror.com/readable-stream/download/readable-stream-2.3.7.tgz", "integrity": "sha1-Hsoc9xGu+BTAT2IlKjamL2yyO1c=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}, "dependencies": {"isarray": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE="}}}, "readdirp": {"version": "3.6.0", "resolved": "https://registry.npmmirror.com/readdirp/download/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "requires": {"picomatch": "^2.2.1"}}, "recursive-iterator": {"version": "3.3.0", "resolved": "https://registry.npmmirror.com/recursive-iterator/download/recursive-iterator-3.3.0.tgz", "integrity": "sha1-TkmM5iJ9jEKy4tSWspa8hmwTRRQ="}, "redis": {"version": "2.8.0", "resolved": "https://registry.npmmirror.com/redis/download/redis-2.8.0.tgz", "integrity": "sha1-ICKI4/WMSfYHnZevehDhMDrhSwI=", "requires": {"double-ended-queue": "^2.1.0-0", "redis-commands": "^1.2.0", "redis-parser": "^2.6.0"}}, "redis-commands": {"version": "1.7.0", "resolved": "https://registry.npmmirror.com/redis-commands/download/redis-commands-1.7.0.tgz", "integrity": "sha1-Fab+otWCgeJ7HNGs+0spPieMOok="}, "redis-parser": {"version": "2.6.0", "resolved": "https://registry.npmmirror.com/redis-parser/download/redis-parser-2.6.0.tgz", "integrity": "sha1-Uu0J2srBCPGmMcB+m2mUHnoZUEs="}, "reduce-component": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/reduce-component/-/reduce-component-1.0.1.tgz", "integrity": "sha512-y0wyCcdQul3hI3xHfIs0vg/jSbboQc/YTOAqaxjFG7At+XSexduuOqBVL9SmOLSwa/ldkbzVzdwuk9s2EKTAZg=="}, "reflect-metadata": {"version": "0.1.13", "resolved": "https://registry.npmmirror.com/reflect-metadata/download/reflect-metadata-0.1.13.tgz", "integrity": "sha1-Z648pXyXKiqhZCsQ/jY/4y1J3Ag="}, "registry-auth-token": {"version": "4.2.1", "resolved": "https://registry.npmmirror.com/registry-auth-token/download/registry-auth-token-4.2.1.tgz", "integrity": "sha1-bXtABkQZGJcszV/tzUHcMix5slA=", "dev": true, "requires": {"rc": "^1.2.8"}}, "registry-url": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/registry-url/download/registry-url-5.1.0.tgz", "integrity": "sha1-6YM0tQ1UNLgRNrROxjjZwgCcUAk=", "dev": true, "requires": {"rc": "^1.2.8"}}, "request": {"version": "2.88.2", "resolved": "https://registry.npmmirror.com/request/-/request-2.88.2.tgz", "integrity": "sha512-MsvtOrfG9ZcrOwAW+Qi+F6HbD0CWXEh9ou77uOb7FM2WPhwT7smM833PzanhJLsgXjN89Ir6V2PczXNnMpwKhw==", "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "dependencies": {"form-data": {"version": "2.3.3", "resolved": "https://registry.npmmirror.com/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "qs": {"version": "6.5.3", "resolved": "https://registry.npmmirror.com/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA=="}}}, "request-promise": {"version": "4.2.6", "resolved": "https://registry.npmmirror.com/request-promise/-/request-promise-4.2.6.tgz", "integrity": "sha512-HCHI3DJJUakkOr8fNoCc73E5nU5bqITjOYFMDrKHYOXWXrgD/SBaC7LjwuPymUprRyuF06UK7hd/lMHkmUXglQ==", "requires": {"bluebird": "^3.5.0", "request-promise-core": "1.1.4", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}}, "request-promise-core": {"version": "1.1.4", "resolved": "https://registry.npmmirror.com/request-promise-core/-/request-promise-core-1.1.4.tgz", "integrity": "sha512-TTbAfBBRdWD7aNNOoVOBH4pN/KigV6LyapYNNlAPA8JwbovRti1E88m3sYAwsLi5ryhPKsE9APwnjFTgdUjTpw==", "requires": {"lodash": "^4.17.19"}}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I="}, "require-main-filename": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/require-main-filename/download/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE="}, "requires-port": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8="}, "resolve": {"version": "1.20.0", "resolved": "https://registry.npmmirror.com/resolve/download/resolve-1.20.0.tgz", "integrity": "sha1-YpoBP7P3B1XW8LeTXMHCxTeLGXU=", "requires": {"is-core-module": "^2.2.0", "path-parse": "^1.0.6"}}, "resolve-path": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/resolve-path/download/resolve-path-1.4.0.tgz", "integrity": "sha1-xL2p9e+y/OZSR4c6s2u02DT+Fvc=", "requires": {"http-errors": "~1.6.2", "path-is-absolute": "1.0.1"}, "dependencies": {"depd": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak="}, "http-errors": {"version": "1.6.3", "resolved": "https://registry.npmmirror.com/http-errors/download/http-errors-1.6.3.tgz?cache=0&sync_timestamp=1593407634112&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fhttp-errors%2Fdownload%2Fhttp-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "requires": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}}, "inherits": {"version": "2.0.3", "resolved": "https://registry.npmmirror.com/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4="}, "setprototypeof": {"version": "1.1.0", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY="}}}, "responselike": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/responselike/download/responselike-1.0.2.tgz", "integrity": "sha1-kYcg7ztjHFZCvgaPFa3lpG9Loec=", "dev": true, "requires": {"lowercase-keys": "^1.0.0"}}, "retry": {"version": "0.10.1", "resolved": "https://registry.npmmirror.com/retry/download/retry-0.10.1.tgz", "integrity": "sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q="}, "rfdc": {"version": "1.3.0", "resolved": "https://registry.npmmirror.com/rfdc/download/rfdc-1.3.0.tgz", "integrity": "sha1-0LfEQasnINBdxM8m4ByJYx2doIs="}, "safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmmirror.com/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0="}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo="}, "sax": {"version": "1.2.4", "resolved": "https://registry.npmmirror.com/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw=="}, "semver": {"version": "6.3.0", "resolved": "https://registry.npmmirror.com/semver/download/semver-6.3.0.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-6.3.0.tgz", "integrity": "sha1-7gpkyK9ejO6mdoexM3YeG+y9HT0="}, "semver-diff": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/semver-diff/download/semver-diff-3.1.1.tgz", "integrity": "sha1-Bfd85Z8yXgDicGr9Z7tQbdscoys=", "dev": true, "requires": {"semver": "^6.3.0"}}, "set-blocking": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc="}, "setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/setprototypeof/download/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ="}, "shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmmirror.com/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "requires": {"shebang-regex": "^1.0.0"}}, "shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM="}, "shimmer": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/shimmer/download/shimmer-1.2.1.tgz", "integrity": "sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc="}, "side-channel": {"version": "1.0.4", "resolved": "https://registry.npmmirror.com/side-channel/download/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "signal-exit": {"version": "3.0.5", "resolved": "https://registry.npmmirror.com/signal-exit/download/signal-exit-3.0.5.tgz?cache=0&sync_timestamp=1632948374592&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsignal-exit%2Fdownload%2Fsignal-exit-3.0.5.tgz", "integrity": "sha1-nj6MwMdamUcrRDIQM6dwLnc4JS8="}, "simple-concat": {"version": "1.0.1", "resolved": "https://registry.npmmirror.com/simple-concat/download/simple-concat-1.0.1.tgz", "integrity": "sha1-9Gl2CCujXCJj8cirXt/ibEHJVS8=", "optional": true}, "simple-get": {"version": "2.8.1", "resolved": "https://registry.npmmirror.com/simple-get/download/simple-get-2.8.1.tgz", "integrity": "sha1-DiLpHUV12HYgYgvJEwjVenf0S10=", "optional": true, "requires": {"decompress-response": "^3.3.0", "once": "^1.3.1", "simple-concat": "^1.0.0"}}, "snappy": {"version": "6.3.5", "resolved": "https://registry.npmmirror.com/snappy/download/snappy-6.3.5.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsnappy%2Fdownload%2Fsnappy-6.3.5.tgz", "integrity": "sha1-wUuN6o6bwmh4dbXkkdFd2QDmAjw=", "optional": true, "requires": {"bindings": "^1.3.1", "nan": "^2.14.1", "prebuild-install": "5.3.0"}}, "source-map": {"version": "0.6.1", "resolved": "https://registry.npmmirror.com/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true}, "source-map-support": {"version": "0.5.20", "resolved": "https://registry.npmmirror.com/source-map-support/download/source-map-support-0.5.20.tgz", "integrity": "sha1-EhZgifj15ejFaSazd2Mzkt0stsk=", "dev": true, "requires": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "spdx-correct": {"version": "3.1.1", "resolved": "https://registry.npmmirror.com/spdx-correct/download/spdx-correct-3.1.1.tgz", "integrity": "sha1-3s6BrJweZxPl99G28X1Gj6U9iak=", "dev": true, "requires": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "spdx-exceptions": {"version": "2.3.0", "resolved": "https://registry.npmmirror.com/spdx-exceptions/download/spdx-exceptions-2.3.0.tgz", "integrity": "sha1-PyjOGnegA3JoPq3kpDMYNSeiFj0=", "dev": true}, "spdx-expression-parse": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/spdx-expression-parse/download/spdx-expression-parse-3.0.1.tgz", "integrity": "sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=", "dev": true, "requires": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "spdx-license-ids": {"version": "3.0.10", "resolved": "https://registry.npmmirror.com/spdx-license-ids/download/spdx-license-ids-3.0.10.tgz", "integrity": "sha1-DZvszN5wA9bGWNSH3UijLwvzAUs=", "dev": true}, "sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "sshpk": {"version": "1.17.0", "resolved": "https://registry.npmmirror.com/sshpk/-/sshpk-1.17.0.tgz", "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "stack-chain": {"version": "1.3.7", "resolved": "https://registry.npmmirror.com/stack-chain/download/stack-chain-1.3.7.tgz", "integrity": "sha1-0ZLJ/06moiyUxN1FkXHj8AzqEoU="}, "statuses": {"version": "1.5.0", "resolved": "https://registry.npmmirror.com/statuses/download/statuses-1.5.0.tgz?cache=0&sync_timestamp=1609654090567&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstatuses%2Fdownload%2Fstatuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow="}, "stealthy-require": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/stealthy-require/-/stealthy-require-1.1.1.tgz", "integrity": "sha512-ZnWpYnYugiOVEY5GkcuJK1io5V8QmNYChG62gSit9pQVGErXtrKuPC55ITaVSukmMta5qpMU7vqLt2Lnni4f/g=="}, "streamroller": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/streamroller/download/streamroller-1.0.6.tgz", "integrity": "sha1-gWfYSW7Z8Z8F7ksVjZYRMhuMrNk=", "requires": {"async": "^2.6.2", "date-format": "^2.0.0", "debug": "^3.2.6", "fs-extra": "^7.0.1", "lodash": "^4.17.14"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}}}, "streamsearch": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/streamsearch/-/streamsearch-0.1.2.tgz", "integrity": "sha512-jos8u++JKm0ARcSUTAZXOVC0mSox7Bhn6sBgty73P1f3JGf7yG2clTbBNHUdde/kdvP2FESam+vM6l8jBrNxHA=="}, "string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "requires": {"safe-buffer": "~5.1.0"}}, "string-width": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-1.0.2.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "requires": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}}, "strip-ansi": {"version": "3.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "requires": {"ansi-regex": "^2.0.0"}}, "strip-bom": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/strip-bom/download/strip-bom-2.0.0.tgz", "integrity": "sha1-YhmoVhZSBJHzV4i9vxRHqZx+aw4=", "dev": true, "requires": {"is-utf8": "^0.2.0"}}, "strip-eof": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8="}, "strip-json-comments": {"version": "2.0.1", "resolved": "https://registry.npmmirror.com/strip-json-comments/download/strip-json-comments-2.0.1.tgz", "integrity": "sha1-PFMZQukIwml8DsNEhYwobHygpgo=", "devOptional": true}, "superagent": {"version": "3.8.3", "resolved": "https://registry.npmmirror.com/superagent/download/superagent-3.8.3.tgz", "integrity": "sha1-Rg6g29t9WxG8T3jeulZfhqF44Sg=", "dev": true, "requires": {"component-emitter": "^1.2.0", "cookiejar": "^2.1.0", "debug": "^3.1.0", "extend": "^3.0.0", "form-data": "^2.3.1", "formidable": "^1.2.0", "methods": "^1.1.1", "mime": "^1.4.1", "qs": "^6.5.1", "readable-stream": "^2.3.5"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "requires": {"ms": "^2.1.1"}}}}, "supertest": {"version": "3.4.2", "resolved": "https://registry.npmmirror.com/supertest/download/supertest-3.4.2.tgz?cache=0&sync_timestamp=1629231412398&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsupertest%2Fdownload%2Fsupertest-3.4.2.tgz", "integrity": "sha1-utfeLkPWDSfIyuuKs0pnyKX3Gq0=", "dev": true, "requires": {"methods": "^1.1.2", "superagent": "^3.8.3"}}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmmirror.com/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "requires": {"has-flag": "^3.0.0"}}, "swagger-methods": {"version": "1.0.8", "resolved": "https://registry.npmmirror.com/swagger-methods/download/swagger-methods-1.0.8.tgz", "integrity": "sha1-i6837oYdPHL/ey+q1tdMYLM24u0="}, "swagger-parser": {"version": "5.0.6", "resolved": "https://registry.npmmirror.com/swagger-parser/download/swagger-parser-5.0.6.tgz", "integrity": "sha1-37hwJPjJyMa7fBOQ6zz9Bl50RA4=", "requires": {"call-me-maybe": "^1.0.1", "debug": "^3.1.0", "json-schema-ref-parser": "^5.1.3", "ono": "^4.0.6", "openapi-schema-validation": "^0.4.2", "swagger-methods": "^1.0.4", "swagger-schema-official": "2.0.0-bab6bed", "z-schema": "^3.23.0"}, "dependencies": {"debug": {"version": "3.2.7", "resolved": "https://registry.npmmirror.com/debug/download/debug-3.2.7.tgz?cache=0&sync_timestamp=1625374648057&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdebug%2Fdownload%2Fdebug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "requires": {"ms": "^2.1.1"}}}}, "swagger-schema-official": {"version": "2.0.0-bab6bed", "resolved": "https://registry.npmmirror.com/swagger-schema-official/download/swagger-schema-official-2.0.0-bab6bed.tgz", "integrity": "sha1-cAcEaNbSl3ylI3suUZyn0Gouo/0="}, "tar-fs": {"version": "1.16.3", "resolved": "https://registry.npmmirror.com/tar-fs/download/tar-fs-1.16.3.tgz", "integrity": "sha1-lmpiiEHaLEAQQGqCFny9Xgxy1Qk=", "optional": true, "requires": {"chownr": "^1.0.1", "mkdirp": "^0.5.1", "pump": "^1.0.0", "tar-stream": "^1.1.2"}, "dependencies": {"pump": {"version": "1.0.3", "resolved": "https://registry.npmmirror.com/pump/download/pump-1.0.3.tgz", "integrity": "sha1-Xf6DEcM7v2/BgmH580cCxHwIqVQ=", "optional": true, "requires": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}}}, "tar-stream": {"version": "1.6.2", "resolved": "https://registry.npmmirror.com/tar-stream/download/tar-stream-1.6.2.tgz", "integrity": "sha1-jqVdqzeXIlPZqa+Q/c1VmuQ1xVU=", "optional": true, "requires": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "dependencies": {"bl": {"version": "1.2.3", "resolved": "https://registry.npmmirror.com/bl/download/bl-1.2.3.tgz", "integrity": "sha1-Ho3YAULqyA1xWMnczAR/tiDgNec=", "optional": true, "requires": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}}}, "thenify": {"version": "3.3.1", "resolved": "https://registry.npmmirror.com/thenify/download/thenify-3.3.1.tgz", "integrity": "sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=", "requires": {"any-promise": "^1.0.0"}}, "thenify-all": {"version": "1.6.0", "resolved": "https://registry.npmmirror.com/thenify-all/download/thenify-all-1.6.0.tgz", "integrity": "sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=", "requires": {"thenify": ">= 3.1.0 < 4"}}, "to-buffer": {"version": "1.1.1", "resolved": "https://registry.npmmirror.com/to-buffer/download/to-buffer-1.1.1.tgz", "integrity": "sha1-STvUj2LXxD/N7TE6A9ytsuEhOoA=", "optional": true}, "to-readable-stream": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/to-readable-stream/download/to-readable-stream-1.0.0.tgz", "integrity": "sha1-zgqgwvPfat+FLvtASng+d8BHV3E=", "dev": true}, "to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/toidentifier/download/toidentifier-1.0.0.tgz", "integrity": "sha1-fhvjRw8ed5SLxD2Uo8j013UrpVM="}, "topo": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/topo/download/topo-3.0.3.tgz", "integrity": "sha1-1aZ/suaTB+vusIQC7Coqb1962Vw=", "requires": {"hoek": "6.x.x"}}, "touch": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/touch/download/touch-3.1.0.tgz", "integrity": "sha1-/jZfX3XsntTlaCXgu3bSSrdK+Ds=", "dev": true, "requires": {"nopt": "~1.0.10"}}, "tough-cookie": {"version": "2.5.0", "resolved": "https://registry.npmmirror.com/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "traverse": {"version": "0.3.9", "resolved": "https://registry.npmmirror.com/traverse/download/traverse-0.3.9.tgz", "integrity": "sha1-cXuPIgzAu3tE5AUUwisui7xw2Lk="}, "ts-node": {"version": "6.2.0", "resolved": "https://registry.npmmirror.com/ts-node/download/ts-node-6.2.0.tgz?cache=0&sync_timestamp=1634967366297&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fts-node%2Fdownload%2Fts-node-6.2.0.tgz", "integrity": "sha1-ZaCuKszjGepP16yNfJ8fkMXaa68=", "dev": true, "requires": {"arrify": "^1.0.0", "buffer-from": "^1.1.0", "diff": "^3.1.0", "make-error": "^1.1.1", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "source-map-support": "^0.5.6", "yn": "^2.0.0"}}, "tslib": {"version": "1.14.1", "resolved": "https://registry.npmmirror.com/tslib/download/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "tslint": {"version": "5.20.1", "resolved": "https://registry.npmmirror.com/tslint/download/tslint-5.20.1.tgz?cache=0&sync_timestamp=1618847624538&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftslint%2Fdownload%2Ftslint-5.20.1.tgz", "integrity": "sha1-5AHortoBUrxE3QfmFANPP4DGe30=", "requires": {"@babel/code-frame": "^7.0.0", "builtin-modules": "^1.1.1", "chalk": "^2.3.0", "commander": "^2.12.1", "diff": "^4.0.1", "glob": "^7.1.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "resolve": "^1.3.2", "semver": "^5.3.0", "tslib": "^1.8.0", "tsutils": "^2.29.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmmirror.com/ansi-styles/download/ansi-styles-3.2.1.tgz?cache=0&sync_timestamp=1618995588464&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-styles%2Fdownload%2Fansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmmirror.com/chalk/download/chalk-2.4.2.tgz?cache=0&sync_timestamp=1627646655305&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fchalk%2Fdownload%2Fchalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmmirror.com/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmmirror.com/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "diff": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/diff/download/diff-4.0.2.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fdiff%2Fdownload%2Fdiff-4.0.2.tgz", "integrity": "sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0="}, "semver": {"version": "5.7.1", "resolved": "https://registry.npmmirror.com/semver/download/semver-5.7.1.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-5.7.1.tgz", "integrity": "sha1-qVT5Ma66UI0we78Gnv8MAclhFvc="}}}, "tsscmp": {"version": "1.0.6", "resolved": "https://registry.npmmirror.com/tsscmp/download/tsscmp-1.0.6.tgz", "integrity": "sha1-hbmVg6w1iexL/vgltQAKqRHWBes="}, "tsutils": {"version": "2.29.0", "resolved": "https://registry.npmmirror.com/tsutils/download/tsutils-2.29.0.tgz", "integrity": "sha1-MrSIUBRnrL7dS4VJhnOggSrKC5k=", "requires": {"tslib": "^1.8.1"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "https://registry.npmmirror.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "https://registry.npmmirror.com/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha512-KXXFFdAbFXY4geFIwoyNK+f5Z1b7swfXABfL7HXCmoIWMKU3dmS26672A4EeQtDzLKy7SXmfBu51JolvEKwtGA=="}, "type-detect": {"version": "4.0.8", "resolved": "https://registry.npmmirror.com/type-detect/download/type-detect-4.0.8.tgz", "integrity": "sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=", "dev": true}, "type-fest": {"version": "0.20.2", "resolved": "https://registry.npmmirror.com/type-fest/download/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true}, "type-is": {"version": "1.6.18", "resolved": "https://registry.npmmirror.com/type-is/download/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "typedarray": {"version": "0.0.6", "resolved": "https://registry.npmmirror.com/typedarray/-/typedarray-0.0.6.tgz", "integrity": "sha512-/aCDEGatGvZ2BIk+HmLf4ifCJFwvKFNb9/JeZPMulfgFracn9QFcAf5GO8B/mweUjSoblS5In0cWhqpfs/5PQA=="}, "typedarray-to-buffer": {"version": "3.1.5", "resolved": "https://registry.npmmirror.com/typedarray-to-buffer/download/typedarray-to-buffer-3.1.5.tgz", "integrity": "sha1-qX7nqf9CaRufeD/xvFES/j/KkIA=", "dev": true, "requires": {"is-typedarray": "^1.0.0"}}, "typescript": {"version": "3.9.10", "resolved": "https://registry.npmmirror.com/typescript/download/typescript-3.9.10.tgz", "integrity": "sha1-cPORCselHta+952ngAaQsZv3eLg="}, "typescript-json-schema": {"version": "0.36.0", "resolved": "https://registry.npmmirror.com/typescript-json-schema/download/typescript-json-schema-0.36.0.tgz?cache=0&sync_timestamp=1631896031454&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Ftypescript-json-schema%2Fdownload%2Ftypescript-json-schema-0.36.0.tgz", "integrity": "sha1-CHSKOfzCPZKIGaIPOU/kwPm4hjk=", "requires": {"glob": "~7.1.2", "json-stable-stringify": "^1.0.1", "typescript": "^3.0.1", "yargs": "^12.0.1"}, "dependencies": {"ansi-regex": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-3.0.0.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg="}, "glob": {"version": "7.1.7", "resolved": "https://registry.npmmirror.com/glob/download/glob-7.1.7.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fglob%2Fdownload%2Fglob-7.1.7.tgz", "integrity": "sha1-Oxk+kjPwHULQs/eClLvutBj5SpA=", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8="}, "string-width": {"version": "2.1.1", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-2.1.1.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "requires": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}}, "strip-ansi": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "requires": {"ansi-regex": "^3.0.0"}}, "yargs": {"version": "12.0.5", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-12.0.5.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-12.0.5.tgz", "integrity": "sha1-BfWZe2CWR7ZPZrgeO0sQo2jnrRM=", "requires": {"cliui": "^4.0.0", "decamelize": "^1.2.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^11.1.1"}}}}, "undefsafe": {"version": "2.0.5", "resolved": "https://registry.npmmirror.com/undefsafe/download/undefsafe-2.0.5.tgz", "integrity": "sha1-OHM7kye9zSJtuIn7cjpu/RYubiw=", "dev": true}, "unique-string": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/unique-string/download/unique-string-2.0.0.tgz", "integrity": "sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=", "dev": true, "requires": {"crypto-random-string": "^2.0.0"}}, "universalify": {"version": "0.1.2", "resolved": "https://registry.npmmirror.com/universalify/download/universalify-0.1.2.tgz?cache=0&sync_timestamp=1603180004159&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Funiversalify%2Fdownload%2Funiversalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY="}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "update-notifier": {"version": "5.1.0", "resolved": "https://registry.npmmirror.com/update-notifier/download/update-notifier-5.1.0.tgz", "integrity": "sha1-SrDXx/NqIx3XMWz3cpMT8CFNmtk=", "dev": true, "requires": {"boxen": "^5.0.0", "chalk": "^4.1.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.4.0", "is-npm": "^5.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.1.0", "pupa": "^2.1.1", "semver": "^7.3.4", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}, "dependencies": {"lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmmirror.com/lru-cache/download/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "requires": {"yallist": "^4.0.0"}}, "semver": {"version": "7.3.5", "resolved": "https://registry.npmmirror.com/semver/download/semver-7.3.5.tgz?cache=0&sync_timestamp=1618846864940&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fsemver%2Fdownload%2Fsemver-7.3.5.tgz", "integrity": "sha1-C2Ich5NI2JmOSw5L6Us/EuYBjvc=", "dev": true, "requires": {"lru-cache": "^6.0.0"}}, "yallist": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/yallist/download/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true}}}, "uri-js": {"version": "4.4.1", "resolved": "https://registry.npmmirror.com/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "requires": {"punycode": "^2.1.0"}}, "urijs": {"version": "1.19.7", "resolved": "https://registry.npmmirror.com/urijs/download/urijs-1.19.7.tgz", "integrity": "sha1-T1lOWRE5KP6mPADOaI+zlbEWirk="}, "url-parse-lax": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/url-parse-lax/download/url-parse-lax-3.0.0.tgz", "integrity": "sha1-FrXK/Afb42dsGxmZF3gj1lA6yww=", "dev": true, "requires": {"prepend-http": "^2.0.0"}}, "util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8="}, "uuid": {"version": "3.4.0", "resolved": "https://registry.npmmirror.com/uuid/download/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4="}, "validate-npm-package-license": {"version": "3.0.4", "resolved": "https://registry.npmmirror.com/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "requires": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "validator": {"version": "10.11.0", "resolved": "https://registry.npmmirror.com/validator/download/validator-10.11.0.tgz", "integrity": "sha1-ADEI6m6amHTTHMyeUAaFbM12sig="}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmmirror.com/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "verror": {"version": "1.10.0", "resolved": "https://registry.npmmirror.com/verror/-/verror-1.10.0.tgz", "integrity": "sha512-ZZKSmDAEFOijERBLkmYfJ+vmk3w+7hOLYDNkRCuRuMJGEmqYNCNLyBBFwWKVMhfwaEF3WOd0Zlw86U/WC/+nYw==", "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dependencies": {"core-util-is": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ=="}}}, "which": {"version": "1.3.1", "resolved": "https://registry.npmmirror.com/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "requires": {"isexe": "^2.0.0"}}, "which-module": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho="}, "which-pm-runs": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/which-pm-runs/download/which-pm-runs-1.0.0.tgz", "integrity": "sha1-Zws6+8VS4LVd9rd4DKdGFfI60cs=", "optional": true}, "wide-align": {"version": "1.1.5", "resolved": "https://registry.npmmirror.com/wide-align/download/wide-align-1.1.5.tgz", "integrity": "sha1-3x1MIGhUNp7PPJpImPGyP72dFdM=", "optional": true, "requires": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "widest-line": {"version": "3.1.0", "resolved": "https://registry.npmmirror.com/widest-line/download/widest-line-3.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwidest-line%2Fdownload%2Fwidest-line-3.1.0.tgz", "integrity": "sha1-gpIzO79my0X/DeFgOxNreuFJbso=", "dev": true, "requires": {"string-width": "^4.0.0"}, "dependencies": {"ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/ansi-regex/download/ansi-regex-5.0.1.tgz?cache=0&sync_timestamp=1631634988487&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fansi-regex%2Fdownload%2Fansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmmirror.com/string-width/download/string-width-4.2.3.tgz?cache=0&sync_timestamp=1632421309919&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fstring-width%2Fdownload%2Fstring-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmmirror.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}}}, "wrap-ansi": {"version": "2.1.0", "resolved": "https://registry.npmmirror.com/wrap-ansi/download/wrap-ansi-2.1.0.tgz?cache=0&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrap-ansi%2Fdownload%2Fwrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmmirror.com/wrappy/download/wrappy-1.0.2.tgz?cache=0&sync_timestamp=1619133505879&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fwrappy%2Fdownload%2Fwrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "write-file-atomic": {"version": "3.0.3", "resolved": "https://registry.npmmirror.com/write-file-atomic/download/write-file-atomic-3.0.3.tgz", "integrity": "sha1-Vr1cWlxwSBzRnFcb05q5ZaXeVug=", "dev": true, "requires": {"imurmurhash": "^0.1.4", "is-typedarray": "^1.0.0", "signal-exit": "^3.0.2", "typedarray-to-buffer": "^3.1.5"}}, "xdg-basedir": {"version": "4.0.0", "resolved": "https://registry.npmmirror.com/xdg-basedir/download/xdg-basedir-4.0.0.tgz", "integrity": "sha1-S8jZmEQDaWIl74OhVzy7y0552xM=", "dev": true}, "xml2js": {"version": "0.4.23", "resolved": "https://registry.npmmirror.com/xml2js/-/xml2js-0.4.23.tgz", "integrity": "sha512-ySPiMjM0+pLDftHgXY4By0uswI3SPKLDw/i3UXbnO8M/p28zqexCUoPmQFrYD+/1BzhGJSs2i1ERWKJAtiLrug==", "requires": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}}, "xmlbuilder": {"version": "11.0.1", "resolved": "https://registry.npmmirror.com/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "integrity": "sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA=="}, "xtend": {"version": "4.0.2", "resolved": "https://registry.npmmirror.com/xtend/download/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q="}, "y18n": {"version": "4.0.3", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-4.0.3.tgz", "integrity": "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8="}, "yallist": {"version": "2.1.2", "resolved": "https://registry.npmmirror.com/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI="}, "yargs": {"version": "7.1.2", "resolved": "https://registry.npmmirror.com/yargs/download/yargs-7.1.2.tgz?cache=0&sync_timestamp=1632605487521&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs%2Fdownload%2Fyargs-7.1.2.tgz", "integrity": "sha1-Y6Cl1CFDh5/bswNwdBN04GQdVds=", "dev": true, "requires": {"camelcase": "^3.0.0", "cliui": "^3.2.0", "decamelize": "^1.1.1", "get-caller-file": "^1.0.1", "os-locale": "^1.4.0", "read-pkg-up": "^1.0.1", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^1.0.2", "which-module": "^1.0.0", "y18n": "^3.2.1", "yargs-parser": "^5.0.1"}, "dependencies": {"camelcase": {"version": "3.0.0", "resolved": "https://registry.npmmirror.com/camelcase/download/camelcase-3.0.0.tgz", "integrity": "sha1-MvxLn82vhF/N9+c7uXysImHwqwo=", "dev": true}, "cliui": {"version": "3.2.0", "resolved": "https://registry.npmmirror.com/cliui/download/cliui-3.2.0.tgz", "integrity": "sha1-EgYBU3qRbSmUD5NNo7SNWFo5IT0=", "dev": true, "requires": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wrap-ansi": "^2.0.0"}}, "invert-kv": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/invert-kv/download/invert-kv-1.0.0.tgz", "integrity": "sha1-EEqOSqym09jNFXqO+L+rLXo//bY=", "dev": true}, "lcid": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/lcid/download/lcid-1.0.0.tgz", "integrity": "sha1-MIrMr6C8SDo4Z7S28rlQYlHRuDU=", "dev": true, "requires": {"invert-kv": "^1.0.0"}}, "os-locale": {"version": "1.4.0", "resolved": "https://registry.npmmirror.com/os-locale/download/os-locale-1.4.0.tgz?cache=0&sync_timestamp=1633618411079&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fos-locale%2Fdownload%2Fos-locale-1.4.0.tgz", "integrity": "sha1-IPnxeuKe00XoveWDsT0gCYA8FNk=", "dev": true, "requires": {"lcid": "^1.0.0"}}, "which-module": {"version": "1.0.0", "resolved": "https://registry.npmmirror.com/which-module/download/which-module-1.0.0.tgz", "integrity": "sha1-u6Y8qGGUiZT/MHc2CJ47lgJsKk8=", "dev": true}, "y18n": {"version": "3.2.2", "resolved": "https://registry.npmmirror.com/y18n/download/y18n-3.2.2.tgz", "integrity": "sha1-hckBvWRwznH8S7cjrSCbcPfyhpY=", "dev": true}, "yargs-parser": {"version": "5.0.1", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-5.0.1.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-5.0.1.tgz", "integrity": "sha1-ft4ynB2M274gm9Jc25kOmx67s5Q=", "dev": true, "requires": {"camelcase": "^3.0.0", "object.assign": "^4.1.0"}}}}, "yargs-parser": {"version": "11.1.1", "resolved": "https://registry.npmmirror.com/yargs-parser/download/yargs-parser-11.1.1.tgz?cache=0&sync_timestamp=1624233514145&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyargs-parser%2Fdownload%2Fyargs-parser-11.1.1.tgz", "integrity": "sha1-h5oIZZc7yp9rq1y987HGfsfTvPQ=", "requires": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "ylru": {"version": "1.2.1", "resolved": "https://registry.npmmirror.com/ylru/download/ylru-1.2.1.tgz", "integrity": "sha1-9Xa2M0FUeYnB3nuiiHYJI7J/6E8="}, "yn": {"version": "2.0.0", "resolved": "https://registry.npmmirror.com/yn/download/yn-2.0.0.tgz?cache=0&sync_timestamp=1628974764210&other_urls=https%3A%2F%2Fregistry.npmmirror.com%2Fyn%2Fdownload%2Fyn-2.0.0.tgz", "integrity": "sha1-5a2ryKz0CPY4X8dklWhMiOavaJo=", "dev": true}, "z-schema": {"version": "3.25.1", "resolved": "https://registry.npmmirror.com/z-schema/download/z-schema-3.25.1.tgz", "integrity": "sha1-fhRmO+K5YAPZOKVvZE+4VhZD+34=", "requires": {"commander": "^2.7.1", "core-js": "^2.5.7", "lodash.get": "^4.0.0", "lodash.isequal": "^4.0.0", "validator": "^10.0.0"}}}}