variables:
  SERVICE_CODE: 'distribution-operation-web'
  ARTIFACT_PATH: $CI_PROJECT_NAME.zip
  ARTIFACT_YUN_PATH: ${CI_PROJECT_NAME}-yun.zip

image: $CI_IMAGE_FRONT_NODE_14

stages:
  - package
  - upload

################################
#    前置检查
################################
before_script:
  - preCheck

################################
#    编译打包(test)
################################
package-fed-test:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run eslint
    - npm run eslint-stat -- --service=$SERVICE_CODE --uid=$GITLAB_USER_EMAIL --branch=$CI_COMMIT_REF_NAME
    - npm run build:test
    - cp deploy/env/setenv_test.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
    - zip -rq ../$ARTIFACT_YUN_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
      - $ARTIFACT_YUN_PATH
      - web/test-code-temp
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
      
  only:
    - /^(release|hotfix|dev|feature).*$/

################################
#    编译打包(online)
################################
package-fed-online:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:online
    - cp deploy/env/setenv_online.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
    - zip -rq ../$ARTIFACT_YUN_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
      - $ARTIFACT_YUN_PATH
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
  only:
    - /^(release|hotfix).*$/

################################
#    编译打包(dev)
################################
package-fed-dev:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:betayun
    - cp deploy/env/setenv_betayun.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
    - zip -rq ../$ARTIFACT_YUN_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
      - $ARTIFACT_YUN_PATH
    expire_in: 1d
  cache:
    key: "$CI_PROJECT_NAME"
    paths:
      - server/node_modules/
      - web/node_modules/
  only:
    - betayun


################################
# 测试服上传代码制品自动发布  --autoDeploy=true --clusterId=2 // 2：云外测试环境的集群id
################################
upload_artifacts-test:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=test --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --autoDeploy=true --clusterId=2

  tags:
    - ci-front
  only:
    - /^(release|hotfix|dev|feature).*$/
  dependencies:
    - package-fed-test

################################
# 线上传代码制品
################################
upload_artifacts-online:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION
  tags:
    - ci-front
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-online

################################
# 回归测试服上传代码制品  --autoDeploy=true --clusterId=0
################################
upload_artifacts-dev:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=dev --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION --module=dev --autoDeploy=true --clusterId=0
  tags:
    - betayun-runner
  only:
    - betayun
  dependencies:
    - package-fed-dev


################################
# 测试环境镜像制品构建、上传及自动发布 --  --autoDeploy=true --autoDeploy=true --clusterId=${clusterId} --ldcCode=${ldcCode} 在cmdb上可以查到
################################
test_image_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/$SERVICE_CODE:${IMAGE_TAG}"
    - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
    - docker build --pull -t ${IMAGE_NAME}  .
    - eval opera docker $OPERA_ARGS --env=test --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG}
  tags:
    - ci-front
  only:
    - /^(release|hotfix|dev|feature).*$/
  dependencies:
    - package-fed-test

################################
#    线上环境镜像制品构建及上传
################################
online_image_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/$SERVICE_CODE:${IMAGE_TAG}"
    - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
    - docker build --pull -t ${IMAGE_NAME}  .
    - eval opera docker $OPERA_ARGS --env=online --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG}
  tags:
    - ci-front
  only:
    - /^(release|hotfix).*$/
  dependencies:
    - package-fed-online

################################
#    发环境镜像制品构建、上传及自动发布 --autoDeploy=true --clusterId=${clusterId} --ldcCode=${ldcCode} 在cmdb上可以查到
################################
dev_image_upload:
  stage: upload
  script:
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - IMAGE_TAG="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - IMAGE_NAME="${HARBOR_URL}/${HARBOR_APP_DIRECTOR}/reverse-flow:${IMAGE_TAG}"
    # 强制删除target并重新新建一个target文件夹，解压云上打包文件到target文件夹下，将target/setenv.sh移到当前目录下
    - rm -rf target && mkdir target && unzip $ARTIFACT_YUN_PATH -d target && mv target/setenv.sh .
    # 使用 Dockerfile 创建镜像，标签为IMAGE_NAME，并尝试去更新镜像的新版本
    # 指定beta云环境的dockerfile
    - docker build --pull -t ${IMAGE_NAME} -f Dockerfile-betayun .
    # 将制品上传至opera  根据这两个字段来执行opera的发布--clusterId=44 --ldcCode=cluster1
    - eval opera docker $OPERA_ARGS --env=dev --imageName=${IMAGE_NAME} --imageTag=${IMAGE_TAG} --module=dev   --autoDeploy=true --clusterId=44 --ldcCode=cluster1
  tags:
    - betayun-runner
  only:
    - betayun
  dependencies:
    - package-fed-dev
