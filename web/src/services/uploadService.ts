import { axiosService } from '@sharkr/request'
import axios from 'axios'

const contextPath = '/admin';
let url = `${contextPath}/xhr/nos/uploadStatic.json`
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    url = '/xhr/nos/upload.json'
} else if (window.location.host.indexOf('remote.yx.mail.netease.com') > -1) {
    url = `${contextPath}/xhr/nos/uploadStatic.json`
}

// 上传文件
export async function uploadFile(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const res = await axios({
        method: 'post',
        url: url,
        data: formData,
        headers: {
            'Content-Type': 'application/json',
            'Name': 'name'
        },
        xsrfCookieName: 'YX_CSRF_TOKEN',
        xsrfHeaderName: 'Yx-Csrf-Token'
    })
    return res
}

export function downloadFile(file: any) {
    const pathName = window.location.pathname
        .split('/')
        .filter(v => v)
        .join('/')
    let fKey = file.url || file.fileUrl
    const reg = /(http|https):\/\/([\w.]+\/?)\S*/
    if (reg.test(fKey)) {
        const fKeyArr = fKey.split('/')
        fKey = fKeyArr[fKeyArr.length - 1]
    }
    return `/${pathName}/xhr/file/downLoadFile.do?fileName=${encodeURIComponent(
        file.name || file.fileName
    )}&fileKey=${fKey}`
}
