import { PlainObject } from '@shark/core';
import { axiosService } from '@sharkr/request';
import { BasePicQueryVO, PageVO, BasePictureListBO, SkuPictureUploadVO, SpuPictureUploadVO } from '../interfaces';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    ApiHost = '';
}

// 获取底图列表
export const getBasePictureList = (params?: BasePicQueryVO): Promise<any> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/list`, params);

// 上传底图
export const uploadBaseSpuPicture = (params: SpuPictureUploadVO[]): Promise<any> =>
    axiosService.postByJson(`${ApiHost}/xhr/base/picture/spu/upload`, params);

// 上传SKU图
export const uploadBaseSkuPicture = (params: SkuPictureUploadVO[]): Promise<any> =>
  axiosService.postByJson(`${ApiHost}/xhr/base/picture/sku/upload`, params);