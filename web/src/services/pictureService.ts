import { PlainObject } from '@shark/core';
import { axiosService } from '@sharkr/request';
import { BasePicQueryVO, PageVO, BasePictureListBO } from '../interfaces';

let ApiHost = '/xhr/returnFirst';
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
    ApiHost = '';
}

// 获取底图列表
export const getBasePictureList = (params?: BasePicQueryVO): Promise<any> =>
    axiosService.get(`${ApiHost}/xhr/base/picture/spu/list`, params);
