import React, { useEffect, useState } from 'react'
import { Modal, Form, Select, Upload, Button, Radio, message } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { uploadFile } from '../../../services/uploadService'
import './PictureUploadModal.scss'
import { PIC_TYPE, SkuPictureUploadVO, TEAM_ID } from '../../../interfaces'
import { loadPicSize } from '../../../utils'
import { getUserInfo } from '@sharkr/components'
import { uploadBaseSpuPicture } from '../../../services'

const { Option } = Select

export interface PictureUploadModalProps {
  teamId?: TEAM_ID
  imageType?: 'main' | 'sku'
  isOpen: boolean
  onCancel: () => void
  onOk?: (results: SkuPictureUploadVO[]) => void
}

interface ValidationResult {
  isValid: boolean
  errorMessage?: string
}

const PictureUploadModal: React.FC<PictureUploadModalProps> = ({
  teamId,
  imageType = 'main',
  isOpen,
  onCancel,
  onOk
}) => {
  const [form] = Form.useForm()
  const [fileList, setFileList] = useState<any[]>([])

  // 监听表单值变化
  const watchedValues = Form.useWatch([], form)

  // 重置状态
  const resetState = () => {
    form.resetFields()
    setFileList([])
  }

  // 图片尺寸验证
  const validateImageDimensions = (file: File, imageType: string): Promise<ValidationResult> => {
    return new Promise((resolve) => {
      loadPicSize(URL.createObjectURL(file)).then(({ width, height }) => {
        if (imageType === 'main') {
          // 商品主图尺寸验证
          const validSizes = [
            { width: 750, height: 1000 },
            { width: 800, height: 800 },
            { width: 800, height: 1200 }
          ]
          
          const isValidSize = validSizes.some(size => 
            size.width === width && size.height === height
          )
          
          if (!isValidSize) {
            resolve({
              isValid: false,
              errorMessage: '尺寸不正确'
            })
          } else {
            resolve({ isValid: true })
          }
        } else {
          // SKU透底图暂无特殊尺寸要求
          resolve({ isValid: true })
        }
      })
    })
  }

  // 文件名验证
  const validateFileName = (fileName: string, imageType: string): ValidationResult => {
    const nameWithoutExt = fileName.replace(/\.(jpg|jpeg|png)$/i, '')
    
    if (imageType === 'main') {
      // 商品主图命名规则：严选SPUID_渠道商品ID 或 严选SPUID_渠道商品ID_数字
      const mainImagePattern = /^\d+_\d+(_\d+)?$/
      if (!mainImagePattern.test(nameWithoutExt)) {
        return {
          isValid: false,
          errorMessage: '命名格式不正确'
        }
      }
    } else if (imageType === 'sku') {
      // SKU透底图命名规则
      const skuPattern = /^\d+(_tb|_jd)?(_\d+)?$/
      
      if (!skuPattern.test(nameWithoutExt)) {
        return {
          isValid: false,
          errorMessage: `命名格式不正确`
        }
      }
    }
    
    return { isValid: true }
  }

  // 文件格式验证
  const validateFileFormat = (fileName: string): ValidationResult => {
    const validFormats = /\.(jpg|jpeg|png)$/i
    if (!validFormats.test(fileName)) {
      return {
        isValid: false,
        errorMessage: '图片格式只支持jpg、jpeg、png'
      }
    }
    return { isValid: true }
  }

  // 综合验证单个文件
  const validateFile = async (file: File, imageType: string): Promise<ValidationResult> => {
    // 格式验证
    const formatResult = validateFileFormat(file.name)
    if (!formatResult.isValid) {
      return formatResult
    }

    // 文件名验证
    const nameResult = validateFileName(file.name, imageType)
    if (!nameResult.isValid) {
      return nameResult
    }

    // 尺寸验证
    const dimensionResult = await validateImageDimensions(file, imageType)
    if (!dimensionResult.isValid) {
      return dimensionResult
    }

    return { isValid: true }
  }

  // 自定义上传处理
  const handleCustomUpload = async (options: any) => {
    const { file, onSuccess, onError, onProgress } = options

    try {
      const values = form.getFieldsValue()
      const { imageType } = values

      // 验证文件
      const validation = await validateFile(file, imageType)

      if (!validation.isValid) {
        onError(new Error(validation.errorMessage))
        return
      }

      // 上传文件
      onProgress({ percent: 50 })
      const response = await uploadFile(file)

      if (response.data.code === 200) {
        onSuccess(response.data.data)
      } else {
        onError(new Error('上传失败'))
      }
    } catch (error) {
      onError(error)
    }
  }

  // 处理文件变化
  const handleFileChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList)
  }

  // 取消
  const handleCancel = () => {
    resetState()
    onCancel()
  }

  // 提交
  const handleOk = async () => {
    const values = await form.validateFields()
    // 从fileList中提取上传结果
    const successList = fileList.filter(file => file.status === 'done')
    if (values.imageType === 'main') {
      // 商品底图
      

    } else {
      // SKU
    }
    const results: any[] = []
    for (const file of successList) {
      const info = await extractInfo(file.name, file.response)
      console.log('info', info)
      if (info) {
        results.push({
          picName: file.name,
          picUrl: file.response,
          // createUser: getUserInfo().email
          picType: PIC_TYPE.NORMAL,
          ...info
        })
      }
    }
    if (results.length === 0) {
      message.error('没有可以上传的图片！')
      return
    }
    await uploadBaseSpuPicture(results)
    if (onOk) {
      onOk(results)
    }
    resetState()
    onCancel()
  }

  // 从文件名中提取信息
  const extractInfo = async (picName: string, picUrl: string) => {
    const { width, height } = await loadPicSize(picUrl)
    const imageType = form.getFieldValue('imageType')
    const nameWithoutExt = picName.replace(/\.(jpg|jpeg|png)$/i, '')
    if (imageType === 'main') {
      const teamId = form.getFieldValue('teamId')
      const reg = /^(\d+)_(\d+)(_\d+)?$/
      const match = nameWithoutExt.match(reg)
      if (match) {
        const spuId = match[1]
        const channelProductId = match[2]
        return { spuId, channelProductId, picSize: `${width}*${height}`, teamId }
      }
    } else {
      const reg = /^(\d+)(_tb|_jd)?(_\d+)?$/
      const match = nameWithoutExt.match(reg)
      if (match) {
        const skuId = match[1]
        const teamId = match[2] ? match[2] === '_tb' ? TEAM_ID.TB : TEAM_ID.JD : undefined
        return { skuId, teamId, picSize: `${width}*${height}` }
      }
    }
  }

  useEffect(() => {
    setFileList([])
  }, [watchedValues?.imageType])

  return (
    <Modal
      open={isOpen}
      title="上传图片"
      onCancel={handleCancel}
      destroyOnClose
      maskClosable={false}
      keyboard={false}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="ok" type="primary" onClick={handleOk}>
          确定
        </Button>
      ]}
    >
      <Form
        form={form}
        initialValues={{
          teamId,
          imageType
        }}
        {...{
            labelCol: { span: 6 },
            wrapperCol: { span: 18 }
        }}
      >
          <Form.Item
            name="imageType"
            label="图片类型"
            rules={[{ required: true, message: '请选择图片类型' }]}
          >
            <Radio.Group>
              <Radio value="main">渠道商品主图</Radio>
              <Radio value="sku">SKU透底图</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.imageType !== currentValues.imageType
            }
          >
          {({ getFieldValue }) => {
            const imageType = getFieldValue('imageType')
            return imageType === 'main' ? (
              <Form.Item
                name="teamId"
                label="归属渠道"
                rules={[{ required: true, message: '请选择归属渠道' }]}
              >
                <Select className="sharkr-w-md">
                  <Option value="2">淘系</Option>
                  <Option value="3">京东综合</Option>
                </Select>
              </Form.Item>
            ) : null
          }}
        </Form.Item>
        <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.imageType !== currentValues.imageType
            }
          >
          {({ getFieldValue }) => {
            const imageType = getFieldValue('imageType')
            return imageType === 'main' ? (
              <Form.Item
                name="picType"
                label="选择标签"
                rules={[{ required: true, message: '请选择标签' }]}
              >
                <Select className="sharkr-w-md">
                  <Option value={PIC_TYPE.NORMAL}>无</Option>
                  <Option value={PIC_TYPE.DAILY}>日常</Option>
                  <Option value={PIC_TYPE.PROMOTION}>活动</Option>
                </Select>
              </Form.Item>
            ) : null
          }}
        </Form.Item>

        <Form.Item required label="上传图片">
          <Upload
            multiple
            fileList={fileList}
            customRequest={handleCustomUpload}
            showUploadList={{
              showDownloadIcon: true,
            }}
            onChange={handleFileChange}
            accept=".jpg,.jpeg,.png"
            listType="text"
          >
            <Button icon={<UploadOutlined />}>批量选择</Button>
          </Upload>
          <div className="upload-tips">
            <div className="tip-item">仅支持 .png 或 .jpg 格式的图片文件</div>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default PictureUploadModal
