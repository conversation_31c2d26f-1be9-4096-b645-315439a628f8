# PictureBaseList 底图列表页面

## 概述
PictureBaseList 是一个用于展示和管理底图的列表页面，支持多条件搜索和分页功能。

## 功能特性

### 1. 搜索功能
- **产品编码ID**: 支持按渠道商品ID搜索
- **产品名称**: 支持按商品名称模糊搜索  
- **操作人**: 支持按操作人搜索
- **二级BU ID**: 支持按二级业务单元ID搜索
- **SPU ID**: 支持按标准产品单元ID搜索
- **时间范围**: 支持按时间范围筛选

### 2. 列表展示
- **SPU ID**: 显示标准产品单元ID
- **产品名称**: 显示商品名称
- **BU信息**: 显示一级和二级业务单元信息
- **团队统计**: 以标签形式显示各团队的图片数量统计
- **操作**: 提供查看详情等操作按钮

### 3. 分页功能
- 支持页码跳转
- 支持页面大小调整
- 显示总数统计信息

## 技术实现

### 接口定义
- `BasePicQueryVO`: 查询参数接口
- `PageVO<BasePictureListBO>`: 分页返回数据接口
- `BasePictureListBO`: 底图列表项接口
- `BasePictureStatisticsBO`: 图片统计接口
- `BasePictureBO`: 底图详情接口

### API接口
- **接口地址**: `/xhr/base/picture/spu/list`
- **请求方式**: GET
- **参数**: BasePicQueryVO
- **返回**: PageVO<BasePictureListBO>

### 组件结构
```
web/src/pages/picture/base/
├── index.ts                 # 导出文件
├── index.scss              # 样式文件
├── PictureBaseList.tsx     # 主组件
└── README.md               # 说明文档
```

### 路由配置
- **路径**: `/picture/base/list`
- **组件**: `PictureBaseList`
- **权限**: 需要UMC权限验证

## 使用方式

### 1. 访问页面
在浏览器中访问: `http://your-domain/#/picture/base/list`

### 2. 搜索操作
1. 在搜索表单中输入搜索条件
2. 点击"搜索"按钮执行搜索
3. 点击"重置"按钮清空搜索条件

### 3. 分页操作
- 点击页码进行翻页
- 使用页面大小选择器调整每页显示数量
- 使用快速跳转功能直接跳转到指定页面

## 样式说明
- 使用项目统一的 `sharkr-section` 样式框架
- 搜索表单使用 `sharkr-form-inline` 内联样式
- 表格使用 `sharkr-table` 样式
- 分页区域右对齐显示

## 扩展说明
- 组件支持响应式布局
- 表格支持横向滚动
- 支持加载状态显示
- 集成了错误处理机制
