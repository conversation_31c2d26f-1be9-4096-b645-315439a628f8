import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Row, Col, Table, Pagination, Space, Breadcrumb, Form, message, Tag } from 'antd';
import { Link } from 'react-router-dom';
import moment from 'moment';
import { SharkRForm } from '@sharkr/form';
import './index.scss';
import { getBasePictureList } from '../../../services/pictureService';
import {
    BasePicQueryVO,
    BasePictureListBO,
    BasePictureStatisticsBO,
    BasePictureBO,
    IAntDPaginationProps,
    IPictureQueryProps
} from '../../../interfaces';

export const PictureBaseList: React.FC = () => {
    const [form] = Form.useForm();
    const searchRef = useRef<any>();

    const [searchParam, setSearchParam] = useState<IPictureQueryProps>({
        pageNo: 1,
        pageSize: 10,
        channelProductId: undefined,
        itemName: undefined,
        operator: undefined,
        secondBuId: undefined,
        spuId: undefined,
        startTime: undefined,
        endTime: undefined
    });

    const [dataSource, setDataSource] = useState<BasePictureListBO[]>([]);
    const [pagination, setPagination] = useState<IAntDPaginationProps>({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [loading, setLoading] = useState(false);

    // 搜索表单配置
    const searchSchema: any = [
        {
            key: 'channelProductId',
            type: 'Input',
            label: '产品编码ID',
            props: {
                placeholder: '请输入产品编码ID',
                allowClear: true
            }
        },
        {
            key: 'itemName',
            type: 'Input',
            label: '产品名称',
            props: {
                placeholder: '请输入产品名称',
                allowClear: true
            }
        },
        {
            key: 'operator',
            type: 'Input',
            label: '操作人',
            props: {
                placeholder: '请输入操作人',
                allowClear: true
            }
        },
        {
            key: 'secondBuId',
            type: 'InputNumber',
            label: '二级BU ID',
            props: {
                placeholder: '请输入二级BU ID',
                style: { width: '100%' }
            }
        },
        {
            key: 'spuId',
            type: 'InputNumber',
            label: 'SPU ID',
            props: {
                placeholder: '请输入SPU ID',
                style: { width: '100%' }
            }
        },
        {
            key: 'timeRange',
            type: 'RangePicker',
            label: '时间范围',
            props: {
                placeholder: ['开始时间', '结束时间'],
                showTime: true,
                format: 'YYYY-MM-DD HH:mm:ss'
            }
        }
    ];

    // 表格列定义
    const columns: any = [
        {
            title: '严选商品ID',
            dataIndex: 'spuId',
            key: 'spuId',
            width: 100
        },
        {
            title: '严选商品名称',
            dataIndex: 'name',
            key: 'name',
            width: 200
        },
        {
            title: '归属BU',
            key: 'buInfo',
            width: 150,
              render: (record: BasePictureListBO) => `${record.buName}-${record.secondBuName}`
        },
        {
            title: '主图信息',
            dataIndex: 'pictureStatisticsList',
            key: 'pictureStatisticsList',
            render: (statisticsList: BasePictureStatisticsBO[]) => (
                <div>
                    {statisticsList?.map((stats, index) => (
                        <Tag key={index} color="blue">
                            {stats.teamName}
                            :
                            {stats.totalCount}
                            张
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: 'SKU透底图',
            dataIndex: 'pictureStatisticsList',
            key: 'pictureStatisticsList',
            render: (statisticsList: BasePictureStatisticsBO[]) => (
                <div>
                    {statisticsList?.map((stats, index) => (
                        <Tag key={index} color="blue">
                            {stats.teamName}
                            :
                            {stats.totalCount}
                            张
                        </Tag>
                    ))}
                </div>
            )
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (record: BasePictureListBO) => (
                <Space>
                    <Button type="link" size="small">
                        查看详情
                    </Button>
                </Space>
            )
        }
    ];

    // 获取列表数据
    const getList = async () => {
        setLoading(true);
        try {
            const queryParams: BasePicQueryVO = {
                channelProductId: searchParam.channelProductId || '',
                endTime: searchParam.endTime || 0,
                itemName: searchParam.itemName || '',
                operator: searchParam.operator || '',
                secondBuId: searchParam.secondBuId || 0,
                spuId: searchParam.spuId || 0,
                startTime: searchParam.startTime || 0
            };

            const res = await getBasePictureList(queryParams);
            if (res && res.code === 200) {
                const { result, paginationVO } = res.data || { result: [], paginationVO: {} };
                const antdPagination: IAntDPaginationProps = {
                    current: paginationVO.page,
                    pageSize: paginationVO.size,
                    total: paginationVO.total
                };
                setPagination(antdPagination);
                setDataSource(result);
            } else {
                message.error('获取底图列表失败');
            }
        } catch (error) {
            message.error('获取底图列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 搜索
    const onSearch = () => {
        const formValues = searchRef.current?.getFieldsValue();
        const newSearchParam: IPictureQueryProps = {
            ...searchParam,
            pageNo: 1,
            ...formValues
        };

        // 处理时间范围
        if (formValues.timeRange && formValues.timeRange.length === 2) {
            newSearchParam.startTime = formValues.timeRange[0].valueOf();
            newSearchParam.endTime = formValues.timeRange[1].valueOf();
        }

        setSearchParam(newSearchParam);
    };

    // 重置搜索
    const onReset = () => {
        searchRef.current?.resetFields();
        const resetParam: IPictureQueryProps = {
            pageNo: 1,
            pageSize: 10,
            channelProductId: undefined,
            itemName: undefined,
            operator: undefined,
            secondBuId: undefined,
            spuId: undefined,
            startTime: undefined,
            endTime: undefined
        };
        setSearchParam(resetParam);
    };

    // 分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        const newSearchParam: IPictureQueryProps = {
            ...searchParam,
            pageNo: page,
            pageSize: pageSize || searchParam.pageSize
        };
        setSearchParam(newSearchParam);
    };

    useEffect(() => {
        getList();
    }, [searchParam]);

    return (
        <>
            <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
                <Breadcrumb.Item>图片管理</Breadcrumb.Item>
                <Breadcrumb.Item>底图列表</Breadcrumb.Item>
            </Breadcrumb>
            <section className="sharkr-section picture-base-list">
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">底图列表</span>
                </div>
                <div className="sharkr-section-content">
                    <SharkRForm
                        className="sharkr-form-inline searchForm"
                        ref={searchRef}
                        schema={searchSchema}
                        {...{
                            labelCol: { span: 5 },
                            wrapperCol: { span: 19 }
                        }}
                    />
                    <Row>
                        <Col span={8} xl={8} xxl={6}>
                            <Row>
                                <Col offset={5}>
                                    <Space>
                                        <Button type="primary" onClick={onSearch}>
                                            搜索
                                        </Button>
                                        <Button onClick={onReset}>
                                            重置
                                        </Button>
                                    </Space>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Table
                        className="sharkr-table"
                        columns={columns}
                        dataSource={dataSource}
                        key="pictureBaseListTable"
                        pagination={false}
                        rowKey="spuId"
                        loading={loading}
                        scroll={{ x: 800 }}
                    />
                    {dataSource.length ? (
                        <div className={'pagination-area'}>
                            <Pagination
                                {...pagination}
                                defaultCurrent={1}
                                onChange={handlePaginationChange}
                                showSizeChanger
                                showQuickJumper
                                showTotal={(total, range) =>
                                    `第 ${range[0]}-${range[1]} 条/总共 ${total} 条`
                                }
                            />
                        </div>
                    ) : null}
                </div>
            </section>
        </>
    );
};
